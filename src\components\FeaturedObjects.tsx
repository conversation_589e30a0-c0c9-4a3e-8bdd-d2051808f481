import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, MapPin, Calendar } from "lucide-react";

const featuredObjects = [
  {
    id: 1,
    title: "Perceuse sans fil Bosch",
    description: "Perceuse professionnelle avec batterie et accessoires",
    price: 15,
    image: "https://images.unsplash.com/photo-1558618166-fbd00c73cd8b?w=400&h=300&fit=crop",
    rating: 4.8,
    reviews: 24,
    location: "Dakar, Sénégal",
    owner: "Amadou D.",
    category: "Bricolage"
  },
  {
    id: 2,
    title: "Canon EOS R6 + Objectifs",
    description: "Appareil photo professionnel avec 3 objectifs",
    price: 45,
    image: "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop",
    rating: 5.0,
    reviews: 18,
    location: "Abidjan, Côte d'Ivoire",
    owner: "<PERSON><PERSON>",
    category: "Photo/Vidéo"
  },
  {
    id: 3,
    title: "Système de son JBL",
    description: "Enceintes professionnelles pour événements",
    price: 35,
    image: "https://images.unsplash.com/photo-1545454675-3531b543be5d?w=400&h=300&fit=crop",
    rating: 4.9,
    reviews: 31,
    location: "Lagos, Nigeria",
    owner: "Kwame A.",
    category: "Sono/Musique"
  },
  {
    id: 4,
    title: "Tente 4 places Coleman",
    description: "Tente familiale imperméable avec accessoires",
    price: 20,
    image: "https://images.unsplash.com/photo-1504851149312-7a075b496cc7?w=400&h=300&fit=crop",
    rating: 4.7,
    reviews: 15,
    location: "Casablanca, Maroc",
    owner: "Aicha M.",
    category: "Camping"
  },
  {
    id: 5,
    title: "Projecteur 4K Epson",
    description: "Vidéoprojecteur haute définition pour présentations",
    price: 30,
    image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop",
    rating: 4.6,
    reviews: 12,
    location: "Tunis, Tunisie",
    owner: "Omar B.",
    category: "Informatique"
  },
  {
    id: 6,
    title: "MacBook Pro M2",
    description: "Ordinateur portable pour création et développement",
    price: 40,
    image: "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop",
    rating: 4.9,
    reviews: 27,
    location: "Rabat, Maroc",
    owner: "Youssef H.",
    category: "Informatique"
  }
];

const FeaturedObjects = () => {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Objets populaires
            </h2>
            <p className="text-lg text-muted-foreground">
              Découvrez les objets les plus demandés par notre communauté
            </p>
          </div>
          <Button variant="outline" className="hidden md:block">
            Voir tout
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredObjects.map((object, index) => (
            <Card 
              key={object.id}
              className="group cursor-pointer hover:shadow-card transition-all duration-300 hover:scale-[1.02] animate-fade-in overflow-hidden border-0"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <div className="relative overflow-hidden">
                <img
                  src={object.image}
                  alt={object.title}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <Badge className="absolute top-3 left-3 bg-white/90 text-primary hover:bg-white">
                  {object.category}
                </Badge>
                <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full p-1">
                  <div className="flex items-center space-x-1 px-2 py-1">
                    <Star className="w-3 h-3 text-yellow-500 fill-current" />
                    <span className="text-xs font-medium">{object.rating}</span>
                  </div>
                </div>
              </div>

              <CardContent className="p-6">
                <h3 className="font-semibold text-lg mb-2 group-hover:text-primary transition-colors">
                  {object.title}
                </h3>
                <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                  {object.description}
                </p>

                <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4" />
                    <span>{object.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4" />
                    <span>{object.reviews} avis</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-2xl font-bold text-primary">
                      {object.price}€
                    </span>
                    <span className="text-muted-foreground text-sm">/jour</span>
                  </div>
                  <Button variant="premium" size="sm">
                    <Calendar className="w-4 h-4 mr-2" />
                    Réserver
                  </Button>
                </div>

                <div className="flex items-center space-x-2 mt-4 pt-4 border-t">
                  <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {object.owner.charAt(0)}
                    </span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Propriétaire: {object.owner}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-8 md:hidden">
          <Button variant="outline" className="w-full">
            Voir tous les objets
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedObjects;