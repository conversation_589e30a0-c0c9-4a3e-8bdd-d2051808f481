// Test des corrections d'affichage pour AfroRent Hub

export const testDisplayFixes = () => {
  console.log('🔧 TEST DES CORRECTIONS D\'AFFICHAGE');
  console.log('===================================');
  console.log('Date:', new Date().toLocaleString('fr-FR'));
  console.log('URL actuelle:', window.location.href);
  console.log('');

  const results = {
    objetDetail: {},
    dashboard: {},
    overall: 'PENDING'
  };

  // Test 1: Page Détail d'Objet
  console.log('📱 1. TEST PAGE DÉTAIL D\'OBJET');
  console.log('------------------------------');
  
  const testObjetDetail = () => {
    const currentPath = window.location.pathname;
    const isObjetDetailPage = currentPath.startsWith('/objet/');
    
    if (isObjetDetailPage) {
      console.log('✅ Sur la page détail d\'objet');
      
      // Test du header avec bouton retour
      const backButton = document.querySelector('button[class*="inline-flex"]');
      console.log(`   ${backButton ? '✅' : '❌'} Bouton retour présent`);
      
      // Test du titre de l'objet
      const objectTitle = document.querySelector('h1');
      if (objectTitle) {
        console.log(`   ✅ Titre objet: "${objectTitle.textContent}"`);
        console.log(`   ${objectTitle.textContent?.includes('Perceuse') ? '✅' : '❌'} Titre correct`);
      } else {
        console.log('   ❌ Titre objet manquant');
      }
      
      // Test des badges
      const categoryBadge = document.querySelector('[class*="Badge"]');
      console.log(`   ${categoryBadge ? '✅' : '❌'} Badge catégorie présent`);
      
      const availabilityBadge = document.querySelector('[class*="text-green"]');
      console.log(`   ${availabilityBadge ? '✅' : '❌'} Badge disponibilité présent`);
      
      // Test de la localisation
      const locationElement = document.querySelector('[class*="MapPin"]')?.parentElement;
      if (locationElement) {
        console.log(`   ✅ Localisation: "${locationElement.textContent}"`);
        console.log(`   ${locationElement.textContent?.includes('Abidjan') ? '✅' : '❌'} Format localisation correct`);
      } else {
        console.log('   ❌ Localisation manquante');
      }
      
      // Test de la section prix
      const priceSection = document.querySelector('[class*="text-3xl"][class*="font-bold"]');
      if (priceSection) {
        console.log(`   ✅ Prix: "${priceSection.textContent}"`);
        console.log(`   ${priceSection.textContent?.includes('FCFA') ? '✅' : '❌'} Format prix correct`);
      } else {
        console.log('   ❌ Section prix manquante');
      }
      
      // Test des boutons d'action
      const reserveButton = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent?.includes('Réserver')
      );
      console.log(`   ${reserveButton ? '✅' : '❌'} Bouton réservation présent`);
      
      const contactButton = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent?.includes('Contacter')
      );
      console.log(`   ${contactButton ? '✅' : '❌'} Bouton contact présent`);
      
      // Test des informations propriétaire
      const ownerSection = document.querySelector('h3')?.textContent?.includes('Propriétaire');
      console.log(`   ${ownerSection ? '✅' : '❌'} Section propriétaire présente`);
      
      const ownerName = document.querySelector('[class*="font-medium"]');
      if (ownerName && ownerName.textContent?.includes('Moussa')) {
        console.log(`   ✅ Nom propriétaire: "${ownerName.textContent}"`);
      } else {
        console.log('   ❌ Nom propriétaire manquant ou incorrect');
      }
      
      // Test de la note
      const ratingElement = Array.from(document.querySelectorAll('*')).find(el => 
        el.textContent?.includes('4.8')
      );
      console.log(`   ${ratingElement ? '✅' : '❌'} Note propriétaire présente`);
      
      results.objetDetail = {
        accessible: true,
        backButton: !!backButton,
        title: !!objectTitle,
        badges: !!(categoryBadge && availabilityBadge),
        location: !!locationElement,
        price: !!priceSection,
        actionButtons: !!(reserveButton && contactButton),
        ownerInfo: !!(ownerSection && ownerName && ratingElement)
      };
      
    } else {
      console.log('ℹ️ Pas sur la page détail d\'objet');
      console.log(`   Page actuelle: ${currentPath}`);
      console.log('   Pour tester: naviguez vers /objet/1');
    }
  };

  testObjetDetail();

  // Test 2: Page Dashboard
  console.log('\n📊 2. TEST PAGE DASHBOARD');
  console.log('-------------------------');
  
  const testDashboard = () => {
    const currentPath = window.location.pathname;
    const isDashboardPage = currentPath === '/dashboard';
    
    if (isDashboardPage) {
      console.log('✅ Sur la page dashboard');
      
      // Test du contenu principal
      const mainContent = document.querySelector('main');
      console.log(`   ${mainContent ? '✅' : '❌'} Contenu principal présent`);
      
      // Test des onglets
      const tabsList = document.querySelector('[role="tablist"]');
      console.log(`   ${tabsList ? '✅' : '❌'} Onglets présents`);
      
      // Test des statistiques
      const statsCards = document.querySelectorAll('[class*="Card"]');
      console.log(`   ${statsCards.length > 0 ? '✅' : '❌'} Cartes statistiques: ${statsCards.length}`);
      
      // Test du titre de bienvenue
      const welcomeTitle = Array.from(document.querySelectorAll('h1, h2')).find(el => 
        el.textContent?.includes('Bienvenue') || el.textContent?.includes('Dashboard')
      );
      console.log(`   ${welcomeTitle ? '✅' : '❌'} Titre de bienvenue présent`);
      
      // Test de l'absence d'écran blanc
      const bodyContent = document.body.textContent?.trim();
      const hasContent = bodyContent && bodyContent.length > 100;
      console.log(`   ${hasContent ? '✅' : '❌'} Contenu affiché (pas d'écran blanc)`);
      
      // Test des erreurs console
      const hasErrors = window.console.error.toString().includes('Error');
      console.log(`   ${!hasErrors ? '✅' : '❌'} Pas d'erreurs console`);
      
      results.dashboard = {
        accessible: true,
        mainContent: !!mainContent,
        tabs: !!tabsList,
        stats: statsCards.length > 0,
        welcomeTitle: !!welcomeTitle,
        hasContent: !!hasContent,
        noErrors: !hasErrors
      };
      
    } else {
      console.log('ℹ️ Pas sur la page dashboard');
      console.log(`   Page actuelle: ${currentPath}`);
      console.log('   Pour tester: naviguez vers /dashboard');
      console.log('   Note: Authentification requise');
    }
  };

  testDashboard();

  // Test 3: Responsive Design
  console.log('\n📱 3. TEST RESPONSIVE DESIGN');
  console.log('----------------------------');
  
  const testResponsive = () => {
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      isMobile: window.innerWidth < 768,
      isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
      isDesktop: window.innerWidth >= 1024
    };

    console.log(`   📐 Viewport: ${viewport.width}x${viewport.height}`);
    console.log(`   📱 Mobile: ${viewport.isMobile ? 'Oui' : 'Non'}`);
    console.log(`   📟 Tablet: ${viewport.isTablet ? 'Oui' : 'Non'}`);
    console.log(`   🖥️ Desktop: ${viewport.isDesktop ? 'Oui' : 'Non'}`);

    // Test des éléments responsive
    const mobileOptimized = document.querySelector('[class*="mobile"]') || 
                           document.querySelector('[class*="sm:"]') ||
                           document.querySelector('[class*="md:"]');
    console.log(`   ${mobileOptimized ? '✅' : '❌'} Classes responsive détectées`);

    // Test de la navigation mobile
    const bottomNav = document.querySelector('nav[class*="bottom"]') ||
                     document.querySelector('[class*="bottom-nav"]');
    console.log(`   ${bottomNav ? '✅' : '❌'} Navigation mobile présente`);

    // Test du layout mobile-first
    const mobileLayout = document.querySelector('[class*="mobile-container"]') ||
                        document.querySelector('[class*="min-h-screen"]');
    console.log(`   ${mobileLayout ? '✅' : '❌'} Layout mobile-first`);
  };

  testResponsive();

  // Test 4: Localisation française
  console.log('\n🇫🇷 4. TEST LOCALISATION FRANÇAISE');
  console.log('----------------------------------');
  
  const testLocalization = () => {
    const frenchTexts = [
      'Retour',
      'Réserver',
      'Contacter',
      'Propriétaire',
      'Disponible',
      'par jour',
      'FCFA'
    ];

    let foundTexts = 0;
    frenchTexts.forEach(text => {
      const found = document.body.textContent?.includes(text);
      if (found) foundTexts++;
      console.log(`   ${found ? '✅' : '❌'} "${text}"`);
    });

    console.log(`   📊 Textes français: ${foundTexts}/${frenchTexts.length}`);
    
    const localizationScore = foundTexts / frenchTexts.length;
    console.log(`   🎯 Score localisation: ${(localizationScore * 100).toFixed(0)}%`);
  };

  testLocalization();

  // Rapport final
  setTimeout(() => {
    console.log('\n🎯 RAPPORT FINAL DES CORRECTIONS');
    console.log('================================');
    
    const objetDetailScore = Object.values(results.objetDetail).filter(Boolean).length;
    const dashboardScore = Object.values(results.dashboard).filter(Boolean).length;
    
    console.log(`📱 Page Détail Objet: ${objetDetailScore}/8 éléments corrigés`);
    console.log(`📊 Page Dashboard: ${dashboardScore}/6 éléments corrigés`);
    
    let overallScore = 'EXCELLENT';
    if (objetDetailScore < 6 || dashboardScore < 4) {
      overallScore = 'À AMÉLIORER';
    } else if (objetDetailScore < 8 || dashboardScore < 6) {
      overallScore = 'BON';
    }
    
    results.overall = overallScore;
    
    console.log(`🏆 Score global: ${overallScore}`);
    
    if (overallScore === 'EXCELLENT') {
      console.log('🎉 TOUTES LES CORRECTIONS APPLIQUÉES AVEC SUCCÈS !');
      console.log('✅ Page détail d\'objet: Formatage et layout corrigés');
      console.log('✅ Page dashboard: Écran blanc résolu');
      console.log('✅ Design responsive: Mobile-first maintenu');
      console.log('✅ Localisation française: Préservée');
    } else {
      console.log('⚠️ AMÉLIORATIONS NÉCESSAIRES');
      console.log('Consultez les détails ci-dessus pour les éléments manquants.');
    }
    
    console.log('\n📋 PROCHAINES ÉTAPES:');
    console.log('1. Tester sur différents viewports');
    console.log('2. Vérifier l\'authentification pour le dashboard');
    console.log('3. Tester la navigation entre les pages');
    console.log('4. Valider l\'expérience utilisateur complète');

    return results;
  }, 2000);

  return results;
};

// Export pour utilisation
export default testDisplayFixes;

// Instructions d'utilisation
console.log(`
🔧 TEST DES CORRECTIONS D'AFFICHAGE

Pour tester les corrections :
1. Naviguez vers /objet/1 pour tester la page détail
2. Ouvrez la console (F12) et exécutez : testDisplayFixes()
3. Naviguez vers /dashboard (authentification requise)
4. Relancez le test pour vérifier le dashboard
`);
