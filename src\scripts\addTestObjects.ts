// Script pour ajouter des objets de test à la base de données

import { supabase } from '@/integrations/supabase/client';

export const addTestObjects = async () => {
  try {
    console.log('🔧 Ajout d\'objets de test...');

    const testObjects = [
      {
        titre: 'Perceuse électrique <PERSON>',
        description: 'Perceuse électrique professionnelle avec coffret d\'accessoires. Parfaite pour tous vos travaux de bricolage.',
        prix_par_jour: 15000,
        caution: 50000,
        categorie_id: 'outils-electriques',
        localisation_ville: 'Abidjan',
        localisation_commune: 'Cocody',
        images: ['https://images.unsplash.com/photo-1572981779307-38b8cabb2407?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      },
      {
        titre: 'Réfrigérateur Samsung 300L',
        description: 'Réfrigérateur moderne avec congélateur. Idéal pour événements ou dépannage temporaire.',
        prix_par_jour: 8000,
        caution: 100000,
        categorie_id: 'cuisine',
        localisation_ville: 'Abidjan',
        localisation_commune: 'Plateau',
        images: ['https://images.unsplash.com/photo-1571175443880-49e1d25b2bc5?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      },
      {
        titre: 'Vélo électrique urbain',
        description: 'Vélo électrique moderne pour vos déplacements en ville. Autonomie 50km.',
        prix_par_jour: 12000,
        caution: 200000,
        categorie_id: 'velos',
        localisation_ville: 'Abidjan',
        localisation_commune: 'Marcory',
        images: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      },
      {
        titre: 'Appareil photo Canon EOS',
        description: 'Appareil photo reflex professionnel avec objectifs. Parfait pour vos événements.',
        prix_par_jour: 25000,
        caution: 300000,
        categorie_id: 'photo-video',
        localisation_ville: 'Bouaké',
        localisation_commune: 'koko',
        images: ['https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      },
      {
        titre: 'Tondeuse à gazon électrique',
        description: 'Tondeuse électrique silencieuse pour l\'entretien de votre jardin.',
        prix_par_jour: 10000,
        caution: 80000,
        categorie_id: 'outils-jardinage',
        localisation_ville: 'Yamoussoukro',
        localisation_commune: 'centre',
        images: ['https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      },
      {
        titre: 'Tapis de course professionnel',
        description: 'Tapis de course électrique avec programmes d\'entraînement intégrés.',
        prix_par_jour: 20000,
        caution: 250000,
        categorie_id: 'fitness',
        localisation_ville: 'Abidjan',
        localisation_commune: 'Yopougon',
        images: ['https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      },
      {
        titre: 'Table de réception 8 personnes',
        description: 'Table ronde élégante pour vos réceptions et événements familiaux.',
        prix_par_jour: 5000,
        caution: 30000,
        categorie_id: 'mobilier-evenement',
        localisation_ville: 'Daloa',
        localisation_commune: 'centre',
        images: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      },
      {
        titre: 'Robe de soirée élégante',
        description: 'Robe de soirée noire taille M, parfaite pour vos événements spéciaux.',
        prix_par_jour: 15000,
        caution: 50000,
        categorie_id: 'vetements',
        localisation_ville: 'Abidjan',
        localisation_commune: 'Treichville',
        images: ['https://images.unsplash.com/photo-1566479179817-c0b5b4b4b1e5?w=400'],
        disponible: true,
        created_at: new Date().toISOString()
      }
    ];

    // Ajouter les objets un par un
    for (const objet of testObjects) {
      const { data, error } = await supabase
        .from('objets')
        .insert(objet)
        .select();

      if (error) {
        console.error(`❌ Erreur lors de l'ajout de "${objet.titre}":`, error);
      } else {
        console.log(`✅ Objet ajouté: "${objet.titre}"`);
      }
    }

    console.log('🎉 Objets de test ajoutés avec succès !');
    console.log('📍 Vous pouvez maintenant tester la page Explorer');

  } catch (error) {
    console.error('❌ Erreur générale:', error);
  }
};

// Instructions d'utilisation
console.log(`
🔧 SCRIPT D'AJOUT D'OBJETS DE TEST

Pour ajouter des objets de test :
1. Ouvrez la console du navigateur (F12)
2. Exécutez : addTestObjects()
3. Testez la page Explorer : /explorer
`);

export default addTestObjects;
