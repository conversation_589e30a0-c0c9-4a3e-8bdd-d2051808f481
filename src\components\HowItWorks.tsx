import { Card, CardContent } from "@/components/ui/card";
import { Search, Calendar, CreditCard, Handshake } from "lucide-react";

const steps = [
  {
    icon: Search,
    title: "Recherchez",
    description: "Trouvez l'objet parfait parmi des milliers d'annonces près de chez vous",
    color: "text-blue-600"
  },
  {
    icon: Calendar,
    title: "Réservez",
    description: "Choisissez vos dates et confirmez votre réservation en quelques clics",
    color: "text-green-600"
  },
  {
    icon: CreditCard,
    title: "Payez",
    description: "Paiement sécurisé en ligne avec protection acheteur incluse",
    color: "text-purple-600"
  },
  {
    icon: Handshake,
    title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    description: "Rencontrez le propriétaire et profitez de votre location",
    color: "text-orange-600"
  }
];

const HowItWorks = () => {
  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Comment ça marche ?
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Louer un objet n'a jamais été aussi simple. Suivez ces 4 étapes pour commencer
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => (
            <Card
              key={index}
              className="text-center group hover:shadow-card transition-all duration-300 animate-fade-in border-0 bg-background"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <CardContent className="p-8">
                <div className="relative mb-6">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary/10 to-primary/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <step.icon className={`w-8 h-8 ${step.color}`} />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                </div>
                <h3 className="font-semibold text-xl mb-3 group-hover:text-primary transition-colors">
                  {step.title}
                </h3>
                <p className="text-muted-foreground">
                  {step.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Connection lines for desktop */}
        <div className="hidden lg:block relative -mt-32 mb-16">
          <div className="flex justify-center items-center space-x-32">
            <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-primary/30 to-primary/30"></div>
            <div className="w-24 h-0.5 bg-gradient-to-r from-primary/30 to-primary/30"></div>
            <div className="w-24 h-0.5 bg-gradient-to-r from-primary/30 to-transparent"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;