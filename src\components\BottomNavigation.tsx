import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/MockAuthContext';
import { cn } from '@/lib/utils';
import { 
  Home, 
  Search, 
  LayoutDashboard, 
  User, 
  Plus,
  LogIn
} from 'lucide-react';

interface NavItem {
  icon: React.ElementType;
  label: string;
  path: string;
  requiresAuth?: boolean;
  authOnly?: boolean;
}

const BottomNavigation = () => {
  const location = useLocation();
  const { user, profile } = useAuth();

  const navItems: NavItem[] = [
    {
      icon: Home,
      label: 'Accueil',
      path: '/',
    },
    {
      icon: Search,
      label: 'Explorer',
      path: '/explorer',
    },
    {
      icon: LayoutDashboard,
      label: 'Dashboard',
      path: '/dashboard',
      requiresAuth: true,
    },
    {
      icon: user ? User : LogIn,
      label: user ? 'Profil' : 'Connexion',
      path: user ? '/dashboard' : '/auth',
    },
  ];

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const shouldShowItem = (item: NavItem) => {
    if (item.requiresAuth && !user) return false;
    if (item.authOnly && user) return false;
    return true;
  };

  return (
    <>
      {/* Floating Action Button for adding items (only for loueurs) */}
      {user && profile?.role === 'loueur' && (
        <div className="fixed bottom-20 right-4 z-50">
          <Link to="/objet/nouveau">
            <button className="w-14 h-14 bg-gradient-primary text-white rounded-full shadow-elegant hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center">
              <Plus className="w-6 h-6" />
            </button>
          </Link>
        </div>
      )}

      {/* Bottom Navigation Bar */}
      <nav className="fixed bottom-0 left-0 right-0 z-40 bg-background/95 backdrop-blur-lg border-t border-border/40 shadow-bottom-nav">
        <div className="max-w-md mx-auto px-4">
          <div className="flex items-center justify-around h-16">
            {navItems.filter(shouldShowItem).map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={cn(
                    "flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1 transition-all duration-300",
                    "hover:scale-105 active:scale-95 touch-feedback"
                  )}
                >
                  <div
                    className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300",
                      active
                        ? "bg-primary text-primary-foreground shadow-elegant scale-110"
                        : "text-muted-foreground hover:text-foreground hover:bg-accent"
                    )}
                  >
                    <Icon className="w-5 h-5" />
                  </div>
                  <span
                    className={cn(
                      "text-xs mt-1 font-medium transition-all duration-300 truncate",
                      active
                        ? "text-foreground font-semibold"
                        : "text-muted-foreground"
                    )}
                  >
                    {item.label}
                  </span>
                </Link>
              );
            })}
          </div>
        </div>
      </nav>
    </>
  );
};

export default BottomNavigation;
