import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/MockAuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AvatarUpload from '@/components/AvatarUpload';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, Save, X, User, Mail, Phone, MapPin, UserCheck } from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  ProfileFormData, 
  ProfileValidationErrors, 
  FormState,
  CITIES,
  COMMUNES_BY_CITY,
  ROLE_DESCRIPTIONS
} from '@/types/profile';
import { 
  validateProfileForm, 
  hasValidationErrors, 
  sanitizeProfileData,
  formatPhoneNumber
} from '@/utils/profileValidation';

interface ProfileEditProps {
  onClose?: () => void;
  isModal?: boolean;
}

const ProfileEdit: React.FC<ProfileEditProps> = ({ onClose, isModal = false }) => {
  const { user, profile, updateProfile } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [formData, setFormData] = useState<ProfileFormData>({
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    ville: '',
    commune: '',
    role: 'locataire'
  });

  const [errors, setErrors] = useState<ProfileValidationErrors>({});
  const [formState, setFormState] = useState<FormState>('idle');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Initialize form data from current profile
  useEffect(() => {
    if (profile) {
      setFormData({
        nom: profile.nom || '',
        prenom: profile.prenom || '',
        email: profile.email || '',
        telephone: profile.telephone || '',
        ville: profile.ville || '',
        commune: profile.commune || '',
        role: profile.role || 'locataire'
      });
    }
  }, [profile]);

  // Track changes
  useEffect(() => {
    if (profile) {
      const hasChanges = 
        formData.nom !== (profile.nom || '') ||
        formData.prenom !== (profile.prenom || '') ||
        formData.email !== (profile.email || '') ||
        formData.telephone !== (profile.telephone || '') ||
        formData.ville !== (profile.ville || '') ||
        formData.commune !== (profile.commune || '') ||
        formData.role !== (profile.role || 'locataire');
      
      setHasUnsavedChanges(hasChanges);
    }
  }, [formData, profile]);

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handlePhoneChange = (value: string) => {
    // Auto-format phone number as user types
    const formatted = formatPhoneNumber(value);
    handleInputChange('telephone', formatted);
  };

  const handleVilleChange = (ville: string) => {
    setFormData(prev => ({ 
      ...prev, 
      ville, 
      commune: '' // Reset commune when ville changes
    }));
    
    if (errors.ville) {
      setErrors(prev => ({ ...prev, ville: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const validationErrors = validateProfileForm(formData);
    setErrors(validationErrors);
    return !hasValidationErrors(validationErrors);
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast({
        title: "Erreur de validation",
        description: "Veuillez corriger les erreurs avant de sauvegarder",
        variant: "destructive"
      });
      return;
    }

    setFormState('loading');

    try {
      const sanitizedData = sanitizeProfileData(formData);
      await updateProfile(sanitizedData);
      
      setFormState('success');
      setHasUnsavedChanges(false);
      
      toast({
        title: "Profil mis à jour",
        description: "Vos informations ont été sauvegardées avec succès"
      });

      // Close modal or navigate back after short delay
      setTimeout(() => {
        if (isModal && onClose) {
          onClose();
        } else {
          navigate('/dashboard');
        }
      }, 1000);

    } catch (error) {
      setFormState('error');
      toast({
        title: "Erreur de sauvegarde",
        description: "Une erreur s'est produite lors de la sauvegarde",
        variant: "destructive"
      });
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        "Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir quitter ?"
      );
      if (!confirmLeave) return;
    }

    if (isModal && onClose) {
      onClose();
    } else {
      navigate('/dashboard');
    }
  };

  const availableCommunes = formData.ville ? COMMUNES_BY_CITY[formData.ville] || [] : [];

  return (
    <div className={cn(
      "w-full max-w-2xl mx-auto",
      isModal ? "p-0" : "p-4"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {!isModal && (
            <Button variant="ghost" size="icon" onClick={handleCancel}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
          )}
          <h1 className="text-xl font-bold">Modifier le profil</h1>
        </div>
        {hasUnsavedChanges && (
          <div className="text-sm text-muted-foreground">
            Modifications non sauvegardées
          </div>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Informations personnelles</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Avatar Upload Section */}
          <div className="flex flex-col items-center space-y-3 py-4 border-b border-border/50">
            <h3 className="text-sm font-medium text-muted-foreground">Photo de profil</h3>
            <AvatarUpload size="xl" />
          </div>
          {/* Name fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="prenom" className="text-sm font-medium">
                Prénom <span className="text-destructive">*</span>
              </Label>
              <Input
                id="prenom"
                value={formData.prenom}
                onChange={(e) => handleInputChange('prenom', e.target.value)}
                className={cn(
                  errors.prenom && "border-destructive form-field-error",
                  "transition-all duration-200"
                )}
                placeholder="Votre prénom"
              />
              {errors.prenom && (
                <p className="text-sm text-destructive animate-slide-up">{errors.prenom}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="nom" className="text-sm font-medium">
                Nom <span className="text-destructive">*</span>
              </Label>
              <Input
                id="nom"
                value={formData.nom}
                onChange={(e) => handleInputChange('nom', e.target.value)}
                className={cn(
                  errors.nom && "border-destructive form-field-error",
                  "transition-all duration-200"
                )}
                placeholder="Votre nom de famille"
              />
              {errors.nom && (
                <p className="text-sm text-destructive animate-slide-up">{errors.nom}</p>
              )}
            </div>
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              Adresse email <span className="text-destructive">*</span>
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={cn("pl-10", errors.email && "border-destructive")}
                placeholder="<EMAIL>"
              />
            </div>
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email}</p>
            )}
          </div>

          {/* Phone */}
          <div className="space-y-2">
            <Label htmlFor="telephone" className="text-sm font-medium">
              Téléphone
            </Label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="telephone"
                type="tel"
                value={formData.telephone}
                onChange={(e) => handlePhoneChange(e.target.value)}
                className={cn("pl-10", errors.telephone && "border-destructive")}
                placeholder="+225 XX XX XX XX XX"
              />
            </div>
            {errors.telephone && (
              <p className="text-sm text-destructive">{errors.telephone}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Format: +225 XX XX XX XX XX
            </p>
          </div>

          {/* Location */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ville" className="text-sm font-medium">
                Ville <span className="text-destructive">*</span>
              </Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground z-10" />
                <Select value={formData.ville} onValueChange={handleVilleChange}>
                  <SelectTrigger className={cn("pl-10", errors.ville && "border-destructive")}>
                    <SelectValue placeholder="Sélectionner une ville" />
                  </SelectTrigger>
                  <SelectContent>
                    {CITIES.map((city) => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {errors.ville && (
                <p className="text-sm text-destructive">{errors.ville}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="commune" className="text-sm font-medium">
                Commune
              </Label>
              <Select 
                value={formData.commune} 
                onValueChange={(value) => handleInputChange('commune', value)}
                disabled={!formData.ville || availableCommunes.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une commune" />
                </SelectTrigger>
                <SelectContent>
                  {availableCommunes.map((commune) => (
                    <SelectItem key={commune} value={commune}>
                      {commune}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Role */}
          <div className="space-y-2">
            <Label htmlFor="role" className="text-sm font-medium">
              Rôle <span className="text-destructive">*</span>
            </Label>
            <div className="relative">
              <UserCheck className="absolute left-3 top-3 h-4 w-4 text-muted-foreground z-10" />
              <Select 
                value={formData.role} 
                onValueChange={(value: 'locataire' | 'loueur') => handleInputChange('role', value)}
              >
                <SelectTrigger className={cn("pl-10", errors.role && "border-destructive")}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(ROLE_DESCRIPTIONS).map(([key, role]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex flex-col">
                        <span className="font-medium">{role.title}</span>
                        <span className="text-xs text-muted-foreground">
                          {role.description}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {errors.role && (
              <p className="text-sm text-destructive">{errors.role}</p>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              onClick={handleSave}
              disabled={formState === 'loading' || !hasUnsavedChanges}
              className="flex-1"
            >
              <Save className="h-4 w-4 mr-2" />
              {formState === 'loading' ? 'Sauvegarde...' : 'Sauvegarder'}
            </Button>
            
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={formState === 'loading'}
              className="flex-1"
            >
              <X className="h-4 w-4 mr-2" />
              Annuler
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileEdit;
