import { <PERSON>, CardContent } from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  Camera, 
  Music, 
  Tent,
  Car,
  Gamepad2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>
} from "lucide-react";

const categories = [
  { name: "Bricolage", icon: Drill, count: 450, color: "text-blue-600" },
  { name: "Photo/Vidéo", icon: Camera, count: 320, color: "text-purple-600" },
  { name: "Sono/Musique", icon: Music, count: 280, color: "text-green-600" },
  { name: "Camping", icon: Tent, count: 210, color: "text-orange-600" },
  { name: "Transport", icon: Car, count: 180, color: "text-red-600" },
  { name: "Gaming", icon: Gamepad2, count: 150, color: "text-indigo-600" },
  { name: "Informatique", icon: Laptop, count: 340, color: "text-cyan-600" },
  { name: "Outils", icon: Wrench, count: 290, color: "text-yellow-600" },
  { name: "<PERSON><PERSON>", icon: Home, count: 380, color: "text-pink-600" },
  { name: "Art/Créatif", icon: Palette, count: 120, color: "text-emerald-600" },
  { name: "<PERSON>", icon: Dumbbell, count: 200, color: "text-violet-600" },
  { name: "Enfants", icon: Baby, count: 160, color: "text-rose-600" }
];

const CategoryGrid = () => {
  return (
    <section className="py-16 bg-secondary/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Explorez par catégorie
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Trouvez rapidement ce que vous cherchez parmi nos catégories populaires
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {categories.map((category, index) => (
            <Card 
              key={category.name}
              className="group cursor-pointer hover:shadow-card transition-all duration-300 hover:scale-105 animate-fade-in border-0 bg-background/80 backdrop-blur-sm"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-6 text-center">
                <div className={`w-12 h-12 mx-auto mb-3 rounded-lg bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  <category.icon className={`w-6 h-6 ${category.color}`} />
                </div>
                <h3 className="font-semibold text-sm mb-1 group-hover:text-primary transition-colors">
                  {category.name}
                </h3>
                <p className="text-xs text-muted-foreground">
                  {category.count} objets
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CategoryGrid;