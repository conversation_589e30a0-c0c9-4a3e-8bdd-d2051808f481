import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ExplorerFilters, { FilterValues } from '@/components/ExplorerFilters';
import { IVORIAN_CITIES } from '@/data/locations';
import { OBJECT_CATEGORIES } from '@/data/categories';

const TestComponents: React.FC = () => {
  const [filters, setFilters] = React.useState<FilterValues>({
    search: '',
    city: '',
    commune: '',
    categories: [],
    priceRange: [0, 100000],
    availableFrom: '',
    availableTo: '',
    sortBy: 'date_desc'
  });

  const handleFiltersChange = (newFilters: FilterValues) => {
    setFilters(newFilters);
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      city: '',
      commune: '',
      categories: [],
      priceRange: [0, 100000],
      availableFrom: '',
      availableTo: '',
      sortBy: 'date_desc'
    });
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Test des Composants</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Cette page teste tous les nouveaux composants pour identifier les erreurs potentielles.</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test ExplorerFilters</CardTitle>
          </CardHeader>
          <CardContent>
            <ExplorerFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={clearFilters}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Données de Localisation</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Nombre de villes: {IVORIAN_CITIES.length}</p>
            <p>Première ville: {IVORIAN_CITIES[0]?.name}</p>
            <p>Communes d'Abidjan: {IVORIAN_CITIES[0]?.communes.length}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Données de Catégories</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Nombre de catégories: {OBJECT_CATEGORIES.length}</p>
            <p>Première catégorie: {OBJECT_CATEGORIES[0]?.name}</p>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4">
              {OBJECT_CATEGORIES.slice(0, 6).map((category) => (
                <div key={category.id} className={`p-2 rounded text-xs ${category.color}`}>
                  {category.name}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>État des Filtres</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-muted p-2 rounded overflow-auto">
              {JSON.stringify(filters, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestComponents;
