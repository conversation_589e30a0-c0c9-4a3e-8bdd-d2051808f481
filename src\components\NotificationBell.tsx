import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/MockAuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Bell,
  BellRing,
  CheckCircle,
  XCircle,
  Star,
  CreditCard,
  ThumbsUp,
  ThumbsDown,
  X,
  Check
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  Notification, 
  NotificationType, 
  NOTIFICATION_TYPE_LABELS, 
  NOTIFICATION_TYPE_ICONS 
} from '@/types/profile';
import { formatDate } from '@/utils/profileValidation';

interface NotificationBellProps {
  className?: string;
}

const NotificationBell: React.FC<NotificationBellProps> = ({ className }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Mock notifications for demo - replace with actual API call
  const mockNotifications: Notification[] = [
    {
      id: '1',
      user_id: user?.id || '',
      type: 'reservation_confirmed',
      title: 'Réservation confirmée',
      message: 'Votre réservation pour "Perceuse Bosch" a été confirmée par le propriétaire.',
      data: { reservation_id: 'res_123', object_title: 'Perceuse Bosch' },
      read: false,
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '2',
      user_id: user?.id || '',
      type: 'payment_received',
      title: 'Paiement reçu',
      message: 'Vous avez reçu un paiement de 15,000 FCFA pour la location de votre tondeuse.',
      data: { amount: 15000, object_title: 'Tondeuse électrique' },
      read: false,
      created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      updated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '3',
      user_id: user?.id || '',
      type: 'system_announcement',
      title: 'Nouvelle fonctionnalité',
      message: 'Découvrez notre nouveau système de notifications pour rester informé en temps réel.',
      data: {},
      read: true,
      created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
      updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    }
  ];

  useEffect(() => {
    if (user) {
      // TODO: Replace with actual API call
      setNotifications(mockNotifications);
    }
  }, [user]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationIcon = (type: NotificationType) => {
    const iconMap = {
      reservation_confirmed: CheckCircle,
      reservation_cancelled: XCircle,
      reservation_completed: Star,
      payment_received: CreditCard,
      object_approved: ThumbsUp,
      object_rejected: ThumbsDown,
      system_announcement: Bell
    };
    return iconMap[type] || Bell;
  };

  const getNotificationColor = (type: NotificationType) => {
    const colorMap = {
      reservation_confirmed: 'text-green-600',
      reservation_cancelled: 'text-red-600',
      reservation_completed: 'text-blue-600',
      payment_received: 'text-emerald-600',
      object_approved: 'text-green-600',
      object_rejected: 'text-red-600',
      system_announcement: 'text-blue-600'
    };
    return colorMap[type] || 'text-gray-600';
  };

  const markAsRead = async (notificationId: string) => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        )
      );
      
      toast({
        title: "Notification marquée comme lue",
        duration: 2000
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de marquer la notification comme lue",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const markAllAsRead = async () => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      setNotifications(prev => 
        prev.map(n => ({ ...n, read: true }))
      );
      
      toast({
        title: "Toutes les notifications marquées comme lues",
        duration: 2000
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de marquer les notifications comme lues",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatNotificationTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return "À l'instant";
    } else if (diffInHours < 24) {
      return `Il y a ${diffInHours}h`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `Il y a ${diffInDays}j`;
    }
  };

  if (!user) return null;

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      {/* Bell Button */}
      <Button
        variant="ghost"
        size="icon"
        className="relative h-10 w-10"
        onClick={() => setIsOpen(!isOpen)}
      >
        {unreadCount > 0 ? (
          <BellRing className="h-5 w-5" />
        ) : (
          <Bell className="h-5 w-5" />
        )}
        
        {/* Notification Badge */}
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Dropdown */}
      {isOpen && (
        <Card className="absolute right-0 top-12 w-80 max-h-96 overflow-hidden shadow-lg border z-50 bg-background">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-semibold">
                Notifications
              </CardTitle>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    disabled={isLoading}
                    className="text-xs h-7"
                  >
                    Tout marquer comme lu
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                  className="h-6 w-6"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="p-0">
            {notifications.length === 0 ? (
              <div className="p-6 text-center text-muted-foreground">
                <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Aucune notification</p>
              </div>
            ) : (
              <div className="max-h-64 overflow-y-auto">
                {notifications.map((notification) => {
                  const IconComponent = getNotificationIcon(notification.type);
                  const iconColor = getNotificationColor(notification.type);
                  
                  return (
                    <div
                      key={notification.id}
                      className={cn(
                        "p-3 border-b border-border/50 hover:bg-muted/50 transition-colors",
                        !notification.read && "bg-primary/5"
                      )}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={cn("mt-0.5", iconColor)}>
                          <IconComponent className="h-4 w-4" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <h4 className={cn(
                              "text-sm font-medium truncate",
                              !notification.read && "font-semibold"
                            )}>
                              {notification.title}
                            </h4>
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => markAsRead(notification.id)}
                                disabled={isLoading}
                                className="h-6 w-6 ml-2 flex-shrink-0"
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                          
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          
                          <p className="text-xs text-muted-foreground mt-2">
                            {formatNotificationTime(notification.created_at)}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default NotificationBell;
