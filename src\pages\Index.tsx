import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useCategories } from '@/hooks/useCategories';
import { useObjects } from '@/hooks/useObjects';
import Navbar from "@/components/Navbar";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, MapPin, Star, ArrowRight, Zap, Wrench, Smartphone, Home, Car, Music } from 'lucide-react';
import Footer from "@/components/Footer";

const Index = () => {
  const { user } = useAuth();
  const { categories } = useCategories();
  const { objects } = useObjects();

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(price).replace('XOF', 'FCFA');
  };

  const getIconForCategory = (iconName?: string) => {
    const iconMap: { [key: string]: any } = {
      zap: Zap,
      wrench: Wrench,
      smartphone: Smartphone,
      home: Home,
      car: Car,
      music: Music
    };
    return iconMap[iconName || 'wrench'] || Wrench;
  };

  const featuredObjects = objects.slice(0, 6);

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-primary text-white py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Louez et partagez en 
            <span className="text-primary-glow"> Côte d'Ivoire</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-white/90 max-w-3xl mx-auto">
            La marketplace #1 pour louer des objets du quotidien entre particuliers. 
            De Abidjan à Bouaké, trouvez tout ce dont vous avez besoin !
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link to="/explorer">
              <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                <Search className="mr-2 h-5 w-5" />
                Commencer à explorer
              </Button>
            </Link>
            
            {!user && (
              <Link to="/auth">
                <Button size="lg" variant="outline" className="w-full sm:w-auto bg-white/10 border-white/20 text-white hover:bg-white hover:text-primary">
                  Rejoindre la communauté
                </Button>
              </Link>
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-glow">1000+</div>
              <div className="text-white/80">Objets disponibles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-glow">500+</div>
              <div className="text-white/80">Utilisateurs actifs</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-glow">10</div>
              <div className="text-white/80">Villes couvertes</div>
            </div>
          </div>
        </div>
      </section>

      {/* Catégories populaires */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Explorez par catégorie</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Trouvez rapidement ce que vous cherchez dans nos catégories les plus populaires
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {categories.slice(0, 8).map((category) => {
              const IconComponent = getIconForCategory(category.icone);
              return (
                <Link key={category.id} to={`/explorer?categorie=${category.id}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardContent className="p-6 text-center">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-8 w-8 text-primary" />
                        </div>
                      </div>
                      <h3 className="font-semibold mb-2">{category.nom}</h3>
                      <p className="text-sm text-muted-foreground">{category.description}</p>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Objets en vedette */}
      <section className="py-16 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Objets populaires</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Découvrez les objets les plus demandés par la communauté
            </p>
          </div>

          {featuredObjects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {featuredObjects.map((objet) => (
                <Link key={objet.id} to={`/objet/${objet.id}`}>
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div className="aspect-square bg-muted relative">
                      {objet.images?.[0] ? (
                        <img
                          src={objet.images[0]}
                          alt={objet.titre}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                          Pas d'image
                        </div>
                      )}
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary">
                          {objet.categories?.nom}
                        </Badge>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-lg mb-2 line-clamp-1">
                        {objet.titre}
                      </h3>
                      <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                        {objet.description}
                      </p>
                      <div className="flex items-center text-sm text-muted-foreground mb-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        {objet.localisation_ville}
                        {objet.localisation_commune && `, ${objet.localisation_commune}`}
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-bold text-primary">
                          {formatPrice(objet.prix_par_jour)}/jour
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Star className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400" />
                          4.8
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-muted-foreground mb-4">
                Aucun objet disponible pour le moment
              </div>
              {user && (
                <Link to="/objet/nouveau">
                  <Button>
                    Soyez le premier à publier un objet
                  </Button>
                </Link>
              )}
            </div>
          )}

          <div className="text-center">
            <Link to="/explorer">
              <Button variant="outline" size="lg">
                Voir tous les objets
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Comment ça marche */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Comment ça marche ?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Louez ou mettez en location vos objets en quelques étapes simples
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">1. Trouvez</h3>
              <p className="text-muted-foreground">
                Recherchez l'objet dont vous avez besoin parmi des milliers d'annonces
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">2. Réservez</h3>
              <p className="text-muted-foreground">
                Contactez le propriétaire et réservez l'objet pour les dates souhaitées
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Home className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">3. Profitez</h3>
              <p className="text-muted-foreground">
                Récupérez l'objet et profitez-en ! N'oubliez pas de laisser un avis
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
