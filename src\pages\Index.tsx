import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/MockAuthContext';
import { useCategories } from '@/hooks/useCategories';
import { useObjects } from '@/hooks/useObjects';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PullToRefresh from '@/components/PullToRefresh';
import { Search, MapPin, Star, ArrowRight, Zap, Wrench, Smartphone, Home, Car, Music, TrendingUp, Users, Map } from 'lucide-react';
import { cn } from '@/lib/utils';

const Index = () => {
  const { user } = useAuth();
  const { categories, refetch: refetchCategories } = useCategories();
  const { objects, refetch: refetchObjects } = useObjects();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleRefresh = async () => {
    await Promise.all([refetchCategories(), refetchObjects()]);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(price).replace('XOF', 'FCFA');
  };

  const getIconForCategory = (iconName?: string) => {
    const iconMap: { [key: string]: any } = {
      zap: Zap,
      wrench: Wrench,
      smartphone: Smartphone,
      home: Home,
      car: Car,
      music: Music
    };
    return iconMap[iconName || 'wrench'] || Wrench;
  };

  const featuredObjects = objects.slice(0, 6);

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="min-h-screen bg-background hide-scrollbar">
      {/* Hero Section - Mobile First */}
      <section className="relative bg-gradient-hero text-white px-4 pt-8 pb-12 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-transparent"></div>
        <div className="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-primary-glow/20 rounded-full blur-lg"></div>

        <div className="relative z-10 text-center">
          <div className={cn(
            "transition-all duration-1000 transform",
            isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
          )}>
            <h1 className="text-3xl sm:text-4xl font-bold mb-4 leading-tight">
              Louez et partagez en
              <span className="block text-white drop-shadow-lg"> Côte d'Ivoire</span>
            </h1>
            <p className="text-lg mb-8 text-white drop-shadow-lg max-w-sm mx-auto leading-relaxed">
              La marketplace #1 pour louer des objets du quotidien entre particuliers
            </p>
          </div>

          <div className={cn(
            "flex flex-col gap-3 mb-12 transition-all duration-1000 delay-300 transform",
            isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
          )}>
            <Link to="/explorer" className="w-full">
              <Button size="lg" variant="secondary" className="w-full h-12 text-base font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                <Search className="mr-2 h-5 w-5" />
                Commencer à explorer
              </Button>
            </Link>

            {!user && (
              <Link to="/auth" className="w-full">
                <Button size="lg" variant="outline" className="w-full h-12 bg-white/10 border-white/20 text-white hover:bg-white hover:text-primary backdrop-blur-sm">
                  Rejoindre la communauté
                </Button>
              </Link>
            )}
          </div>

          {/* Stats - Mobile optimized */}
          <div className={cn(
            "grid grid-cols-3 gap-4 transition-all duration-1000 delay-500 transform",
            isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
          )}>
            <div className="text-center">
              <div className="text-2xl font-bold text-white drop-shadow-lg flex items-center justify-center bg-white/10 rounded-lg py-2 backdrop-blur-sm">
                <TrendingUp className="w-5 h-5 mr-1" />
                1000+
              </div>
              <div className="text-xs text-white drop-shadow-md font-medium mt-1">Objets</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white drop-shadow-lg flex items-center justify-center bg-white/10 rounded-lg py-2 backdrop-blur-sm">
                <Users className="w-5 h-5 mr-1" />
                500+
              </div>
              <div className="text-xs text-white drop-shadow-md font-medium mt-1">Utilisateurs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white drop-shadow-lg flex items-center justify-center bg-white/10 rounded-lg py-2 backdrop-blur-sm">
                <Map className="w-5 h-5 mr-1" />
                10
              </div>
              <div className="text-xs text-white drop-shadow-md font-medium mt-1">Villes</div>
            </div>
          </div>
        </div>
      </section>

      {/* Catégories populaires - Mobile optimized */}
      <section className="py-8 px-4">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Explorez par catégorie</h2>
          <p className="text-muted-foreground text-sm">
            Trouvez rapidement ce que vous cherchez
          </p>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          {categories.slice(0, 6).map((category, index) => {
            const IconComponent = getIconForCategory(category.icone);
            return (
              <Link key={category.id} to={`/explorer?categorie=${category.id}`}>
                <Card
                  className={cn(
                    "hover:shadow-card transition-all duration-300 cursor-pointer group transform hover:scale-105 active:scale-95",
                    "animate-fade-in"
                  )}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <CardContent className="p-4 text-center">
                    <div className="mb-3 flex justify-center">
                      <div className="p-3 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-all duration-300 group-hover:scale-110">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                    </div>
                    <h3 className="font-semibold text-sm mb-1">{category.nom}</h3>
                    <p className="text-xs text-muted-foreground line-clamp-2">{category.description}</p>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {categories.length > 6 && (
          <div className="text-center">
            <Link to="/explorer">
              <Button variant="outline" size="sm" className="text-sm">
                Voir toutes les catégories
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        )}
      </section>

      {/* Objets en vedette - Mobile optimized */}
      <section className="py-8 px-4 bg-muted/30">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Objets populaires</h2>
          <p className="text-muted-foreground text-sm">
            Les plus demandés par la communauté
          </p>
        </div>

        {featuredObjects.length > 0 ? (
          <>
            <div className="grid grid-cols-1 gap-4 mb-6">
              {featuredObjects.slice(0, 4).map((objet, index) => (
                <Link key={objet.id} to={`/objet/${objet.id}`}>
                  <Card
                    className={cn(
                      "overflow-hidden hover:shadow-card transition-all duration-300 cursor-pointer transform hover:scale-105 active:scale-95",
                      "animate-slide-up"
                    )}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex">
                      <div className="w-24 h-24 bg-muted relative flex-shrink-0">
                        {objet.images?.[0] ? (
                          <img
                            src={objet.images[0]}
                            alt={objet.titre}
                            className="w-full h-full object-cover rounded-l-lg"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                            Pas d'image
                          </div>
                        )}
                      </div>
                      <CardContent className="p-3 flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-semibold text-sm line-clamp-1 flex-1">
                            {objet.titre}
                          </h3>
                          {objet.categories?.nom && (
                            <Badge variant="secondary" className="text-xs ml-2 flex-shrink-0">
                              {objet.categories.nom}
                            </Badge>
                          )}
                        </div>
                        <p className="text-muted-foreground text-xs mb-2 line-clamp-1">
                          {objet.description}
                        </p>
                        <div className="flex items-center text-xs text-muted-foreground mb-2">
                          <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span className="truncate">
                            {objet.localisation_ville}
                            {objet.localisation_commune && `, ${objet.localisation_commune}`}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="text-sm font-bold text-primary">
                            {formatPrice(objet.prix_par_jour)}/jour
                          </div>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                            4.8
                          </div>
                        </div>
                      </CardContent>
                    </div>
                  </Card>
                </Link>
              ))}
            </div>

            <div className="text-center">
              <Link to="/explorer">
                <Button variant="outline" className="w-full h-12">
                  Voir tous les objets
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-muted-foreground mb-4 text-sm">
              Aucun objet disponible pour le moment
            </div>
            {user && (
              <Link to="/objet/nouveau">
                <Button size="sm">
                  Soyez le premier à publier un objet
                </Button>
              </Link>
            )}
          </div>
        )}
      </section>

      {/* Comment ça marche - Mobile optimized */}
      <section className="py-8 px-4">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Comment ça marche ?</h2>
          <p className="text-muted-foreground text-sm">
            En quelques étapes simples
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center p-4 bg-card rounded-lg shadow-card">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
              <Search className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-1">1. Trouvez</h3>
              <p className="text-muted-foreground text-sm">
                Recherchez l'objet dont vous avez besoin
              </p>
            </div>
          </div>

          <div className="flex items-center p-4 bg-card rounded-lg shadow-card">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
              <Star className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-1">2. Réservez</h3>
              <p className="text-muted-foreground text-sm">
                Contactez le propriétaire et réservez
              </p>
            </div>
          </div>

          <div className="flex items-center p-4 bg-card rounded-lg shadow-card">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
              <Home className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-1">3. Profitez</h3>
              <p className="text-muted-foreground text-sm">
                Récupérez l'objet et profitez-en !
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to action */}
      <section className="py-8 px-4 bg-gradient-accent text-white">
        <div className="text-center">
          <h3 className="text-xl font-bold mb-2">Prêt à commencer ?</h3>
          <p className="text-white drop-shadow-md text-sm mb-6">
            Rejoignez des milliers d'utilisateurs qui font confiance à AfroRent
          </p>
          <div className="space-y-3">
            <Link to="/explorer" className="block">
              <Button size="lg" variant="secondary" className="w-full h-12">
                <Search className="mr-2 h-5 w-5" />
                Explorer maintenant
              </Button>
            </Link>
            {!user && (
              <Link to="/auth" className="block">
                <Button size="lg" variant="outline" className="w-full h-12 bg-white/10 border-white/20 text-white hover:bg-white hover:text-primary">
                  Créer un compte
                </Button>
              </Link>
            )}
          </div>
        </div>
      </section>
      </div>
    </PullToRefresh>
  );
};

export default Index;
