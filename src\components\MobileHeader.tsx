import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import AfroRentLogo from './AfroRentLogo';
import { Bell, Search, Menu } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileHeaderProps {
  showSearch?: boolean;
  showNotifications?: boolean;
  showMenu?: boolean;
  title?: string;
  className?: string;
}

const MobileHeader: React.FC<MobileHeaderProps> = ({
  showSearch = false,
  showNotifications = false,
  showMenu = false,
  title,
  className
}) => {
  const { user, profile } = useAuth();
  const location = useLocation();

  const getPageTitle = () => {
    if (title) return title;
    
    switch (location.pathname) {
      case '/':
        return 'AfroRent';
      case '/explorer':
        return 'Explorer';
      case '/dashboard':
        return 'Dashboard';
      case '/auth':
        return 'Connexion';
      default:
        return 'AfroRent';
    }
  };

  const isAuthPage = location.pathname === '/auth';

  return (
    <header className={cn(
      "sticky top-0 z-50 bg-background/95 backdrop-blur-lg border-b border-border/40",
      className
    )}>
      <div className="max-w-md mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left side - Logo/Title */}
          <div className="flex items-center space-x-3">
            {showMenu && (
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Menu className="h-5 w-5" />
              </Button>
            )}
            
            <Link to="/" className="flex items-center space-x-2">
              {/* Logo placeholder - will be replaced with actual logo */}
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AR</span>
              </div>
              <div className="flex flex-col">
                <h1 className="text-lg font-bold text-foreground leading-none">
                  {getPageTitle()}
                </h1>
                {location.pathname === '/' && (
                  <span className="text-xs text-muted-foreground leading-none">
                    Marketplace de location
                  </span>
                )}
              </div>
            </Link>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-2">
            {showSearch && (
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Search className="h-4 w-4" />
              </Button>
            )}

            {showNotifications && user && (
              <Button variant="ghost" size="icon" className="h-8 w-8 relative">
                <Bell className="h-4 w-4" />
                {/* Notification badge */}
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full flex items-center justify-center">
                  <span className="text-xs text-destructive-foreground font-bold">3</span>
                </div>
              </Button>
            )}

            {/* User Avatar or Auth Button */}
            {!isAuthPage && (
              <>
                {user && profile ? (
                  <Link to="/dashboard">
                    <Avatar className="h-8 w-8 ring-2 ring-primary/20">
                      <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                        {profile.prenom?.[0]}{profile.nom?.[0]}
                      </AvatarFallback>
                    </Avatar>
                  </Link>
                ) : (
                  <Link to="/auth">
                    <Button variant="outline" size="sm" className="h-8 text-xs px-3">
                      Connexion
                    </Button>
                  </Link>
                )}
              </>
            )}
          </div>
        </div>

        {/* Optional subtitle or breadcrumb */}
        {location.pathname === '/explorer' && (
          <div className="mt-2 text-xs text-muted-foreground">
            Trouvez l'objet parfait pour vos besoins
          </div>
        )}
        
        {location.pathname === '/dashboard' && user && profile && (
          <div className="mt-2 text-xs text-muted-foreground">
            Bienvenue, {profile.prenom}
          </div>
        )}
      </div>
    </header>
  );
};

export default MobileHeader;
