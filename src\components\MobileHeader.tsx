import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import AvatarUpload from './AvatarUpload';
import NotificationBell from './NotificationBell';
import AfroRent<PERSON>ogo from './AfroRentLogo';
import { Search, Menu } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileHeaderProps {
  showSearch?: boolean;
  showNotifications?: boolean;
  showMenu?: boolean;
  title?: string;
  className?: string;
}

const MobileHeader: React.FC<MobileHeaderProps> = ({
  showSearch = false,
  showNotifications = false,
  showMenu = false,
  title,
  className
}) => {
  const { user, profile } = useAuth();
  const location = useLocation();

  const getPageTitle = () => {
    if (title) return title;
    
    switch (location.pathname) {
      case '/':
        return 'AfroRent';
      case '/explorer':
        return 'Explorer';
      case '/dashboard':
        return 'Dashboard';
      case '/auth':
        return 'Connexion';
      default:
        return 'AfroRent';
    }
  };

  const isAuthPage = location.pathname === '/auth';

  return (
    <header className={cn(
      "sticky top-0 z-50 bg-background/95 backdrop-blur-lg border-b border-border/40",
      className
    )}>
      <div className="max-w-md mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left side - Logo/Title */}
          <div className="flex items-center space-x-3">
            {showMenu && (
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Menu className="h-5 w-5" />
              </Button>
            )}
            
            <Link to="/" className="flex items-center space-x-2">
              <AfroRentLogo
                size="sm"
                variant={location.pathname === '/' ? 'full' : 'icon'}
              />
              {location.pathname !== '/' && (
                <div className="flex flex-col">
                  <h1 className="text-lg font-bold text-foreground leading-none">
                    {getPageTitle()}
                  </h1>
                </div>
              )}
            </Link>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-2">
            {showSearch && (
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Search className="h-4 w-4" />
              </Button>
            )}

            {showNotifications && user && (
              <NotificationBell />
            )}

            {/* User Avatar or Auth Button */}
            {!isAuthPage && (
              <>
                {user && profile ? (
                  <Link to="/dashboard">
                    <AvatarUpload size="sm" showUploadButton={false} />
                  </Link>
                ) : (
                  <Link to="/auth">
                    <Button variant="outline" size="sm" className="h-8 text-xs px-3">
                      Connexion
                    </Button>
                  </Link>
                )}
              </>
            )}
          </div>
        </div>

        {/* Optional subtitle or breadcrumb */}
        {location.pathname === '/explorer' && (
          <div className="mt-2 text-xs text-muted-foreground">
            Trouvez l'objet parfait pour vos besoins
          </div>
        )}
        
        {location.pathname === '/dashboard' && user && profile && (
          <div className="mt-2 text-xs text-muted-foreground">
            Bienvenue, {profile.prenom}
          </div>
        )}
      </div>
    </header>
  );
};

export default MobileHeader;
