import React from 'react';
import { cn } from '@/lib/utils';

interface AfroRentLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'full' | 'icon' | 'text';
  className?: string;
}

const AfroRentLogo: React.FC<AfroRentLogoProps> = ({ 
  size = 'md', 
  variant = 'full',
  className 
}) => {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
    xl: 'h-16'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-xl',
    xl: 'text-2xl'
  };

  const LogoIcon = () => (
    <svg
      viewBox="0 0 40 40"
      className={cn(sizeClasses[size], "w-auto")}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Background circle with gradient */}
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="hsl(210, 85%, 45%)" />
          <stop offset="100%" stopColor="hsl(210, 85%, 55%)" />
        </linearGradient>
        <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="hsl(195, 85%, 55%)" />
          <stop offset="100%" stopColor="hsl(210, 85%, 60%)" />
        </linearGradient>
      </defs>
      
      {/* Main circle background */}
      <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" />
      
      {/* African-inspired pattern - geometric shapes */}
      <path
        d="M12 8 L28 8 L32 16 L28 24 L12 24 L8 16 Z"
        fill="white"
        fillOpacity="0.15"
      />
      
      {/* House/rental symbol */}
      <path
        d="M20 10 L28 16 L26 16 L26 26 L14 26 L14 16 L12 16 L20 10 Z"
        fill="white"
        fillOpacity="0.9"
      />
      
      {/* Door */}
      <rect x="18" y="20" width="4" height="6" fill="url(#logoGradient)" />
      
      {/* Window */}
      <rect x="16" y="16" width="2" height="2" fill="url(#logoGradient)" />
      <rect x="22" y="16" width="2" height="2" fill="url(#logoGradient)" />
      
      {/* African pattern accent */}
      <circle cx="20" cy="12" r="1.5" fill="url(#accentGradient)" />
      <circle cx="16" cy="14" r="1" fill="url(#accentGradient)" fillOpacity="0.7" />
      <circle cx="24" cy="14" r="1" fill="url(#accentGradient)" fillOpacity="0.7" />
    </svg>
  );

  const LogoText = () => (
    <div className={cn("font-bold text-foreground", textSizeClasses[size])}>
      <span className="text-primary">Afro</span>
      <span className="text-foreground">Rent</span>
    </div>
  );

  if (variant === 'icon') {
    return (
      <div className={cn("flex items-center", className)}>
        <LogoIcon />
      </div>
    );
  }

  if (variant === 'text') {
    return (
      <div className={cn("flex items-center", className)}>
        <LogoText />
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <LogoIcon />
      <LogoText />
    </div>
  );
};

export default AfroRentLogo;
