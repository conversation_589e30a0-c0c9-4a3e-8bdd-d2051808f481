import React, { useState } from 'react';
import { useObjects } from '@/hooks/useObjects';
import { useCategories } from '@/hooks/useCategories';
import Navbar from '@/components/Navbar';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { MapPin, Search, Filter } from 'lucide-react';
import { Link } from 'react-router-dom';

const Explorer = () => {
  const [filters, setFilters] = useState({
    search: '',
    categorie: '',
    ville: '',
    prixMax: ''
  });

  const { objects, loading } = useObjects({
    ...filters,
    prixMax: filters.prixMax ? parseInt(filters.prixMax) : undefined
  });
  const { categories } = useCategories();

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(price).replace('XOF', 'FCFA');
  };

  const villes = ['Abidjan', 'Bouaké', 'Daloa', 'Yamoussoukro', 'San-Pédro'];

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Explorer les objets</h1>
          <p className="text-muted-foreground">
            Trouvez l'objet parfait pour vos besoins en Côte d'Ivoire
          </p>
        </div>

        {/* Filtres */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Rechercher</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Nom de l'objet..."
                    value={filters.search}
                    onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Catégorie</label>
                <Select
                  value={filters.categorie}
                  onValueChange={(value) => setFilters({ ...filters, categorie: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes les catégories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Toutes les catégories</SelectItem>
                    {categories.map((cat) => (
                      <SelectItem key={cat.id} value={cat.id}>
                        {cat.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Ville</label>
                <Select
                  value={filters.ville}
                  onValueChange={(value) => setFilters({ ...filters, ville: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes les villes" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Toutes les villes</SelectItem>
                    {villes.map((ville) => (
                      <SelectItem key={ville} value={ville}>
                        {ville}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Prix max / jour</label>
                <Input
                  type="number"
                  placeholder="Prix en FCFA"
                  value={filters.prixMax}
                  onChange={(e) => setFilters({ ...filters, prixMax: e.target.value })}
                />
              </div>
            </div>

            <div className="mt-4 flex gap-2">
              <Button
                variant="outline"
                onClick={() => setFilters({ search: '', categorie: '', ville: '', prixMax: '' })}
              >
                <Filter className="h-4 w-4 mr-2" />
                Réinitialiser
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Résultats */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <div className="aspect-square bg-muted animate-pulse" />
                <CardContent className="p-4">
                  <div className="h-4 bg-muted rounded animate-pulse mb-2" />
                  <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <>
            <div className="mb-4 text-sm text-muted-foreground">
              {objects.length} objet{objects.length > 1 ? 's' : ''} trouvé{objects.length > 1 ? 's' : ''}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {objects.map((objet) => (
                <Link key={objet.id} to={`/objet/${objet.id}`}>
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div className="aspect-square bg-muted relative">
                      {objet.images?.[0] ? (
                        <img
                          src={objet.images[0]}
                          alt={objet.titre}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                          Pas d'image
                        </div>
                      )}
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary">
                          {objet.categories?.nom}
                        </Badge>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-lg mb-2 line-clamp-1">
                        {objet.titre}
                      </h3>
                      <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                        {objet.description}
                      </p>
                      <div className="flex items-center text-sm text-muted-foreground mb-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        {objet.localisation_ville}
                        {objet.localisation_commune && `, ${objet.localisation_commune}`}
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-bold text-primary">
                          {formatPrice(objet.prix_par_jour)}/jour
                        </div>
                        {objet.caution > 0 && (
                          <div className="text-xs text-muted-foreground">
                            Caution: {formatPrice(objet.caution)}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>

            {objects.length === 0 && (
              <div className="text-center py-12">
                <div className="text-muted-foreground mb-4">
                  Aucun objet trouvé avec ces critères
                </div>
                <Button
                  variant="outline"
                  onClick={() => setFilters({ search: '', categorie: '', ville: '', prixMax: '' })}
                >
                  Réinitialiser les filtres
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Explorer;