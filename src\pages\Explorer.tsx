import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useObjects } from '@/hooks/useObjects';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import ExplorerFilters, { FilterValues } from '@/components/ExplorerFilters';
import PullToRefresh from '@/components/PullToRefresh';
import LoadingSkeleton from '@/components/LoadingSkeleton';
import { MapPin, Search, Filter, SlidersHorizontal, Star, Grid, List } from 'lucide-react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/utils/profileValidation';
import { getCityById, getCommunesByCity } from '@/data/locations';
import { getCategoryById } from '@/data/categories';

const Explorer = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Initialize filters from URL params
  const [filters, setFilters] = useState<FilterValues>({
    search: searchParams.get('search') || '',
    city: searchParams.get('city') || '',
    commune: searchParams.get('commune') || '',
    categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
    priceRange: [
      parseInt(searchParams.get('minPrice') || '0'),
      parseInt(searchParams.get('maxPrice') || '100000')
    ],
    availableFrom: searchParams.get('availableFrom') || '',
    availableTo: searchParams.get('availableTo') || '',
    sortBy: (searchParams.get('sortBy') as FilterValues['sortBy']) || 'date_desc'
  });

  const { objects, loading, refetch } = useObjects({
    search: filters.search,
    categorie: filters.categories.join(','),
    ville: filters.city,
    commune: filters.commune,
    prixMin: filters.priceRange[0],
    prixMax: filters.priceRange[1],
    dateDebut: filters.availableFrom,
    dateFin: filters.availableTo,
    sortBy: filters.sortBy
  });

  // Update URL params when filters change
  useEffect(() => {
    const params = new URLSearchParams();

    if (filters.search) params.set('search', filters.search);
    if (filters.city) params.set('city', filters.city);
    if (filters.commune) params.set('commune', filters.commune);
    if (filters.categories.length > 0) params.set('categories', filters.categories.join(','));
    if (filters.priceRange[0] > 0) params.set('minPrice', filters.priceRange[0].toString());
    if (filters.priceRange[1] < 100000) params.set('maxPrice', filters.priceRange[1].toString());
    if (filters.availableFrom) params.set('availableFrom', filters.availableFrom);
    if (filters.availableTo) params.set('availableTo', filters.availableTo);
    if (filters.sortBy !== 'date_desc') params.set('sortBy', filters.sortBy);

    setSearchParams(params);
  }, [filters, setSearchParams]);

  const handleRefresh = async () => {
    await refetch();
  };

  const handleFiltersChange = (newFilters: FilterValues) => {
    setFilters(newFilters);
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      city: '',
      commune: '',
      categories: [],
      priceRange: [0, 100000],
      availableFrom: '',
      availableTo: '',
      sortBy: 'date_desc'
    });
  };

  const getLocationText = () => {
    if (filters.commune && filters.city) {
      const city = getCityById(filters.city);
      const communes = city ? getCommunesByCity(filters.city) : [];
      const commune = communes.find(c => c.id === filters.commune);
      return `${commune?.name}, ${city?.name}`;
    } else if (filters.city) {
      const city = getCityById(filters.city);
      return city?.name;
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur-lg border-b border-border/40 px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold">Explorer</h1>
            <p className="text-sm text-muted-foreground">
              {objects.length} objet{objects.length > 1 ? 's' : ''}
              {getLocationText() && ` à ${getLocationText()}`}
            </p>
          </div>

          <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <div className="hidden md:flex items-center space-x-1 bg-muted rounded-lg p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-7 w-7 p-0"
              >
                <Grid className="h-3 w-3" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-7 w-7 p-0"
              >
                <List className="h-3 w-3" />
              </Button>
            </div>

            {/* Mobile Filter Button */}
            <Sheet open={showFilters} onOpenChange={setShowFilters}>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" className="relative">
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Filtres
                  {hasActiveFilters() && (
                    <Badge variant="destructive" className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs">
                      {getActiveFiltersCount()}
                    </Badge>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-[90vh]">
                <SheetHeader>
                  <SheetTitle>Filtres de recherche</SheetTitle>
                </SheetHeader>
                <div className="mt-6">
                  <ExplorerFilters
                    filters={filters}
                    onFiltersChange={handleFiltersChange}
                    onClearFilters={clearFilters}
                  />
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>

      <PullToRefresh onRefresh={handleRefresh} disabled={loading}>
        <div className="flex flex-col lg:flex-row gap-6 p-4">
          {/* Desktop Filters Sidebar */}
          <div className="hidden lg:block w-80 flex-shrink-0">
            <ExplorerFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={clearFilters}
              className="sticky top-24"
            />
          </div>

          {/* Results */}
          <div className="flex-1">
            {loading ? (
              <LoadingSkeleton variant="list" count={6} />
            ) : (
              <>
                {objects.length > 0 ? (
                  <div className={cn(
                    viewMode === 'grid'
                      ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
                      : "space-y-3"
                  )}>
                    {objects.map((objet, index) => (
                      <Link key={objet.id} to={`/objet/${objet.id}`}>
                        <Card
                          className={cn(
                            "overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02] active:scale-95",
                            "animate-slide-up"
                          )}
                          style={{ animationDelay: `${index * 50}ms` }}
                        >
                          {viewMode === 'grid' ? (
                            // Grid View
                            <div>
                              <div className="aspect-[4/3] bg-muted relative">
                                {objet.images?.[0] ? (
                                  <img
                                    src={objet.images[0]}
                                    alt={objet.titre}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                                    Pas d'image
                                  </div>
                                )}
                                {objet.categories?.nom && (
                                  <Badge
                                    variant="secondary"
                                    className="absolute top-2 right-2 text-xs"
                                  >
                                    {objet.categories.nom}
                                  </Badge>
                                )}
                              </div>
                              <CardContent className="p-4">
                                <h3 className="font-semibold text-base mb-2 line-clamp-1">
                                  {objet.titre}
                                </h3>
                                <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                                  {objet.description}
                                </p>
                                <div className="flex items-center text-sm text-muted-foreground mb-3">
                                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                                  <span className="truncate">
                                    {objet.localisation_ville}
                                    {objet.localisation_commune && `, ${objet.localisation_commune}`}
                                  </span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <div className="text-lg font-bold text-primary">
                                    {formatCurrency(objet.prix_par_jour)}/jour
                                  </div>
                                  <div className="flex items-center text-sm text-muted-foreground">
                                    <Star className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400" />
                                    4.8
                                  </div>
                                </div>
                              </CardContent>
                            </div>
                          ) : (
                            // List View
                            <div className="flex">
                              <div className="w-24 h-24 bg-muted relative flex-shrink-0">
                                {objet.images?.[0] ? (
                                  <img
                                    src={objet.images[0]}
                                    alt={objet.titre}
                                    className="w-full h-full object-cover rounded-l-lg"
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                                    Pas d'image
                                  </div>
                                )}
                              </div>
                              <CardContent className="p-3 flex-1 min-w-0">
                                <div className="flex items-start justify-between mb-2">
                                  <h3 className="font-semibold text-sm line-clamp-1 flex-1">
                                    {objet.titre}
                                  </h3>
                                  {objet.categories?.nom && (
                                    <Badge variant="secondary" className="text-xs ml-2 flex-shrink-0">
                                      {objet.categories.nom}
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-muted-foreground text-xs mb-2 line-clamp-1">
                                  {objet.description}
                                </p>
                                <div className="flex items-center text-xs text-muted-foreground mb-2">
                                  <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                                  <span className="truncate">
                                    {objet.localisation_ville}
                                    {objet.localisation_commune && `, ${objet.localisation_commune}`}
                                  </span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <div className="text-sm font-bold text-primary">
                                    {formatCurrency(objet.prix_par_jour)}/jour
                                  </div>
                                  <div className="flex items-center text-xs text-muted-foreground">
                                    <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                                    4.8
                                  </div>
                                </div>
                              </CardContent>
                            </div>
                          )}
                        </Card>
                      </Link>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                      <Search className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Aucun objet trouvé</h3>
                    <p className="text-muted-foreground mb-4">
                      Essayez de modifier vos critères de recherche
                    </p>
                    <Button variant="outline" onClick={clearFilters}>
                      Réinitialiser les filtres
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </PullToRefresh>
    </div>
  );
};

export default Explorer;