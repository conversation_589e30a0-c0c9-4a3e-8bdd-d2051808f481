import React, { useState } from 'react';
import { useObjects } from '@/hooks/useObjects';
import { useCategories } from '@/hooks/useCategories';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import PullToRefresh from '@/components/PullToRefresh';
import LoadingSkeleton from '@/components/LoadingSkeleton';
import { MapPin, Search, Filter, SlidersHorizontal, Star } from 'lucide-react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

const Explorer = () => {
  const [filters, setFilters] = useState({
    search: '',
    categorie: '',
    ville: '',
    prixMax: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  const { objects, loading, refetch } = useObjects({
    ...filters,
    prixMax: filters.prixMax ? parseInt(filters.prixMax) : undefined
  });
  const { categories, refetch: refetchCategories } = useCategories();

  const handleRefresh = async () => {
    await Promise.all([refetch(), refetchCategories()]);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(price).replace('XOF', 'FCFA');
  };

  const villes = ['Abidjan', 'Bouaké', 'Daloa', 'Yamoussoukro', 'San-Pédro'];

  const hasActiveFilters = filters.search || filters.categorie || filters.ville || filters.prixMax;

  const FilterContent = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">Rechercher</label>
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Nom de l'objet..."
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            className="pl-10"
          />
        </div>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Catégorie</label>
        <Select
          value={filters.categorie}
          onValueChange={(value) => setFilters({ ...filters, categorie: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Toutes les catégories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Toutes les catégories</SelectItem>
            {categories.map((cat) => (
              <SelectItem key={cat.id} value={cat.id}>
                {cat.nom}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Ville</label>
        <Select
          value={filters.ville}
          onValueChange={(value) => setFilters({ ...filters, ville: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Toutes les villes" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Toutes les villes</SelectItem>
            {villes.map((ville) => (
              <SelectItem key={ville} value={ville}>
                {ville}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Prix max / jour</label>
        <Input
          type="number"
          placeholder="Prix en FCFA"
          value={filters.prixMax}
          onChange={(e) => setFilters({ ...filters, prixMax: e.target.value })}
        />
      </div>

      <Button
        variant="outline"
        onClick={() => setFilters({ search: '', categorie: '', ville: '', prixMax: '' })}
        className="w-full"
      >
        <Filter className="h-4 w-4 mr-2" />
        Réinitialiser
      </Button>
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Header */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur-lg border-b border-border/40 px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold">Explorer</h1>
            <p className="text-sm text-muted-foreground">
              {objects.length} objet{objects.length > 1 ? 's' : ''}
            </p>
          </div>

          {/* Mobile Filter Button */}
          <Sheet open={showFilters} onOpenChange={setShowFilters}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="relative">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Filtres
                {hasActiveFilters && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full"></div>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[80vh]">
              <SheetHeader>
                <SheetTitle>Filtres de recherche</SheetTitle>
              </SheetHeader>
              <div className="mt-6">
                <FilterContent />
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Search bar - always visible */}
        <div className="mt-3">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher un objet..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      <PullToRefresh onRefresh={handleRefresh} disabled={loading}>
        <div className="px-4 py-4">
          {/* Résultats - Mobile optimized */}
          {loading ? (
            <LoadingSkeleton variant="list" count={6} />
          ) : (
          <>
            {objects.length > 0 ? (
              <div className="space-y-3">
                {objects.map((objet, index) => (
                  <Link key={objet.id} to={`/objet/${objet.id}`}>
                    <Card
                      className={cn(
                        "overflow-hidden hover:shadow-card transition-all duration-300 cursor-pointer transform hover:scale-105 active:scale-95",
                        "animate-slide-up"
                      )}
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      <div className="flex">
                        <div className="w-24 h-24 bg-muted relative flex-shrink-0">
                          {objet.images?.[0] ? (
                            <img
                              src={objet.images[0]}
                              alt={objet.titre}
                              className="w-full h-full object-cover rounded-l-lg"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                              Pas d'image
                            </div>
                          )}
                        </div>
                        <CardContent className="p-3 flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="font-semibold text-sm line-clamp-1 flex-1">
                              {objet.titre}
                            </h3>
                            {objet.categories?.nom && (
                              <Badge variant="secondary" className="text-xs ml-2 flex-shrink-0">
                                {objet.categories.nom}
                              </Badge>
                            )}
                          </div>
                          <p className="text-muted-foreground text-xs mb-2 line-clamp-1">
                            {objet.description}
                          </p>
                          <div className="flex items-center text-xs text-muted-foreground mb-2">
                            <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                            <span className="truncate">
                              {objet.localisation_ville}
                              {objet.localisation_commune && `, ${objet.localisation_commune}`}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-bold text-primary">
                              {formatPrice(objet.prix_par_jour)}/jour
                            </div>
                            <div className="flex items-center text-xs text-muted-foreground">
                              <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                              4.8
                            </div>
                          </div>
                          {objet.caution > 0 && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Caution: {formatPrice(objet.caution)}
                            </div>
                          )}
                        </CardContent>
                      </div>
                    </Card>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="h-8 w-8 text-muted-foreground" />
                </div>
                <div className="text-muted-foreground mb-4">
                  Aucun objet trouvé avec ces critères
                </div>
                <Button
                  variant="outline"
                  onClick={() => setFilters({ search: '', categorie: '', ville: '', prixMax: '' })}
                >
                  Réinitialiser les filtres
                </Button>
              </div>
            )}
          </>
        )}
        </div>
      </PullToRefresh>
    </div>
  );
};

export default Explorer;