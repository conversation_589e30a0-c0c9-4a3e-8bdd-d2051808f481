-- Création des tables pour la marketplace de location d'objets

-- Table des profils utilisateurs
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  email TEXT NOT NULL,
  nom TEXT,
  prenom TEXT,
  telephone TEXT,
  ville TEXT DEFAULT 'Abidjan',
  commune TEXT,
  role TEXT DEFAULT 'locataire' CHECK (role IN ('locataire', 'loueur', 'admin')),
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(user_id),
  UNIQUE(email)
);

-- Table des catégories d'objets
CREATE TABLE public.categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nom TEXT NOT NULL,
  description TEXT,
  icone TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Table des objets à louer
CREATE TABLE public.objets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  titre TEXT NOT NULL,
  description TEXT NOT NULL,
  prix_par_jour INTEGER NOT NULL, -- Prix en FCFA
  caution INTEGER DEFAULT 0, -- Caution en FCFA
  proprietaire_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  categorie_id UUID REFERENCES public.categories(id),
  localisation_ville TEXT DEFAULT 'Abidjan',
  localisation_commune TEXT,
  images TEXT[], -- URLs des images
  disponible BOOLEAN DEFAULT true,
  conditions TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Table des disponibilités
CREATE TABLE public.disponibilites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  objet_id UUID REFERENCES public.objets(id) ON DELETE CASCADE NOT NULL,
  date_debut DATE NOT NULL,
  date_fin DATE NOT NULL,
  disponible BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Table des réservations
CREATE TABLE public.reservations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  objet_id UUID REFERENCES public.objets(id) ON DELETE CASCADE NOT NULL,
  locataire_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  loueur_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  date_debut DATE NOT NULL,
  date_fin DATE NOT NULL,
  prix_total INTEGER NOT NULL, -- Prix total en FCFA
  caution INTEGER DEFAULT 0, -- Caution en FCFA
  statut TEXT DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'confirmee', 'en_cours', 'terminee', 'annulee')),
  message_locataire TEXT,
  stripe_session_id TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Table des avis
CREATE TABLE public.avis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reservation_id UUID REFERENCES public.reservations(id) ON DELETE CASCADE NOT NULL,
  auteur_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  objet_id UUID REFERENCES public.objets(id) ON DELETE CASCADE NOT NULL,
  note INTEGER NOT NULL CHECK (note >= 1 AND note <= 5),
  commentaire TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Table des messages
CREATE TABLE public.messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reservation_id UUID REFERENCES public.reservations(id) ON DELETE CASCADE NOT NULL,
  expediteur_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  destinataire_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  contenu TEXT NOT NULL,
  lu BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Activation de RLS sur toutes les tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.objets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.disponibilites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reservations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.avis ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour profiles
CREATE POLICY "Utilisateurs peuvent voir tous les profils" ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Utilisateurs peuvent créer leur profil" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Utilisateurs peuvent modifier leur profil" ON public.profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- Politiques RLS pour categories (lecture publique)
CREATE POLICY "Tout le monde peut voir les catégories" ON public.categories
  FOR SELECT USING (true);

-- Politiques RLS pour objets
CREATE POLICY "Tout le monde peut voir les objets" ON public.objets
  FOR SELECT USING (true);

CREATE POLICY "Propriétaires peuvent créer des objets" ON public.objets
  FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM public.profiles WHERE id = proprietaire_id));

CREATE POLICY "Propriétaires peuvent modifier leurs objets" ON public.objets
  FOR UPDATE USING (auth.uid() = (SELECT user_id FROM public.profiles WHERE id = proprietaire_id));

CREATE POLICY "Propriétaires peuvent supprimer leurs objets" ON public.objets
  FOR DELETE USING (auth.uid() = (SELECT user_id FROM public.profiles WHERE id = proprietaire_id));

-- Politiques RLS pour disponibilités
CREATE POLICY "Tout le monde peut voir les disponibilités" ON public.disponibilites
  FOR SELECT USING (true);

CREATE POLICY "Propriétaires peuvent gérer les disponibilités" ON public.disponibilites
  FOR ALL USING (auth.uid() = (SELECT p.user_id FROM public.profiles p JOIN public.objets o ON o.proprietaire_id = p.id WHERE o.id = objet_id));

-- Politiques RLS pour réservations
CREATE POLICY "Utilisateurs peuvent voir leurs réservations" ON public.reservations
  FOR SELECT USING (
    auth.uid() = (SELECT user_id FROM public.profiles WHERE id = locataire_id) 
    OR auth.uid() = (SELECT user_id FROM public.profiles WHERE id = loueur_id)
  );

CREATE POLICY "Locataires peuvent créer des réservations" ON public.reservations
  FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM public.profiles WHERE id = locataire_id));

CREATE POLICY "Participants peuvent modifier les réservations" ON public.reservations
  FOR UPDATE USING (
    auth.uid() = (SELECT user_id FROM public.profiles WHERE id = locataire_id) 
    OR auth.uid() = (SELECT user_id FROM public.profiles WHERE id = loueur_id)
  );

-- Politiques RLS pour avis
CREATE POLICY "Tout le monde peut voir les avis" ON public.avis
  FOR SELECT USING (true);

CREATE POLICY "Utilisateurs peuvent créer des avis" ON public.avis
  FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM public.profiles WHERE id = auteur_id));

-- Politiques RLS pour messages
CREATE POLICY "Participants peuvent voir leurs messages" ON public.messages
  FOR SELECT USING (
    auth.uid() = (SELECT user_id FROM public.profiles WHERE id = expediteur_id) 
    OR auth.uid() = (SELECT user_id FROM public.profiles WHERE id = destinataire_id)
  );

CREATE POLICY "Utilisateurs peuvent envoyer des messages" ON public.messages
  FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM public.profiles WHERE id = expediteur_id));

-- Fonction pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour updated_at
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_objets_updated_at
  BEFORE UPDATE ON public.objets
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_reservations_updated_at
  BEFORE UPDATE ON public.reservations
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Création du bucket pour les images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('objets-images', 'objets-images', true);

-- Politiques pour le storage
CREATE POLICY "Images publiques en lecture" ON storage.objects
  FOR SELECT USING (bucket_id = 'objets-images');

CREATE POLICY "Utilisateurs authentifiés peuvent uploader" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'objets-images' AND auth.role() = 'authenticated');

CREATE POLICY "Propriétaires peuvent modifier leurs images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'objets-images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Propriétaires peuvent supprimer leurs images" ON storage.objects
  FOR DELETE USING (bucket_id = 'objets-images' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Insertion des catégories par défaut
INSERT INTO public.categories (nom, description, icone) VALUES
('Électroménager', 'Frigos, lave-linge, micro-ondes...', 'zap'),
('Outils', 'Perceuses, marteaux, scies...', 'wrench'),
('Électronique', 'Ordinateurs, téléphones, caméras...', 'smartphone'),
('Mobilier', 'Tables, chaises, canapés...', 'home'),
('Transport', 'Vélos, motos, voitures...', 'car'),
('Événementiel', 'Sono, éclairage, décoration...', 'music'),
('Sport & Loisirs', 'Vélos, équipements sportifs...', 'dumbbell'),
('Jardinage', 'Tondeuses, outils de jardin...', 'leaf');