import React, { useState, useEffect } from 'react';
import { Link, useLocation, Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useAdmin } from '@/hooks/useAdmin';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  Package, 
  Calendar, 
  Shield, 
  BarChart3, 
  Settings, 
  Menu, 
  X, 
  Home,
  LogOut,
  Bell,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';
import AfroRentLogo from './AfroRentLogo';

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
}

interface NavItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  permission?: string;
  badge?: number;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ 
  children, 
  title = "Administration", 
  className 
}) => {
  const { user, profile, signOut } = useAuth();
  const { isAdmin, hasPermission, userStats, objectStats } = useAdmin();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if user is admin
  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth >= 1024) {
        setSidebarOpen(true);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const navigationItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Tableau de bord',
      icon: BarChart3,
      href: '/admin',
      permission: 'analytics.view'
    },
    {
      id: 'users',
      label: 'Utilisateurs',
      icon: Users,
      href: '/admin/users',
      permission: 'users.view',
      badge: userStats?.pending_verification
    },
    {
      id: 'objects',
      label: 'Objets',
      icon: Package,
      href: '/admin/objects',
      permission: 'objects.view',
      badge: objectStats?.pending_approval
    },
    {
      id: 'reservations',
      label: 'Réservations',
      icon: Calendar,
      href: '/admin/reservations',
      permission: 'reservations.view'
    },
    {
      id: 'moderation',
      label: 'Modération',
      icon: Shield,
      href: '/admin/moderation',
      permission: 'content.moderate'
    },
    {
      id: 'analytics',
      label: 'Analyses',
      icon: BarChart3,
      href: '/admin/analytics',
      permission: 'analytics.view'
    },
    {
      id: 'settings',
      label: 'Configuration',
      icon: Settings,
      href: '/admin/settings',
      permission: 'system.configure'
    }
  ];

  const filteredNavItems = navigationItems.filter(item => 
    !item.permission || hasPermission(item.permission)
  );

  const handleSignOut = async () => {
    await signOut();
  };

  const Sidebar = () => (
    <div className={cn(
      "fixed inset-y-0 left-0 z-50 w-64 bg-background border-r border-border transform transition-transform duration-300 ease-in-out",
      sidebarOpen ? "translate-x-0" : "-translate-x-full",
      "lg:translate-x-0 lg:static lg:inset-0"
    )}>
      {/* Sidebar Header */}
      <div className="flex items-center justify-between h-16 px-4 border-b border-border">
        <Link to="/admin" className="flex items-center space-x-2">
          <AfroRentLogo className="h-8 w-8" />
          <span className="font-bold text-lg">Admin</span>
        </Link>
        
        {isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Admin User Info */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold">
            {profile?.prenom?.[0]}{profile?.nom?.[0]}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {profile?.prenom} {profile?.nom}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {profile?.role === 'super_admin' ? 'Super Admin' : 
               profile?.role === 'admin' ? 'Administrateur' : 'Modérateur'}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {filteredNavItems.map((item) => {
          const isActive = location.pathname === item.href || 
            (item.href !== '/admin' && location.pathname.startsWith(item.href));
          
          return (
            <Link
              key={item.id}
              to={item.href}
              className={cn(
                "flex items-center justify-between w-full px-3 py-2 text-sm rounded-lg transition-colors",
                isActive 
                  ? "bg-primary text-primary-foreground" 
                  : "text-muted-foreground hover:bg-muted hover:text-foreground"
              )}
              onClick={() => isMobile && setSidebarOpen(false)}
            >
              <div className="flex items-center space-x-3">
                <item.icon className="h-4 w-4" />
                <span>{item.label}</span>
              </div>
              {item.badge && item.badge > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {item.badge > 99 ? '99+' : item.badge}
                </Badge>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Sidebar Footer */}
      <div className="p-4 border-t border-border space-y-2">
        <Link
          to="/dashboard"
          className="flex items-center space-x-3 w-full px-3 py-2 text-sm text-muted-foreground hover:bg-muted hover:text-foreground rounded-lg transition-colors"
        >
          <Home className="h-4 w-4" />
          <span>Retour au site</span>
        </Link>
        
        <Button
          variant="ghost"
          className="flex items-center space-x-3 w-full justify-start px-3 py-2 text-sm text-muted-foreground hover:bg-muted hover:text-foreground"
          onClick={handleSignOut}
        >
          <LogOut className="h-4 w-4" />
          <span>Déconnexion</span>
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Sidebar */}
      <Sidebar />

      {/* Mobile Overlay */}
      {isMobile && sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className={cn(
        "flex flex-col min-h-screen transition-all duration-300",
        "lg:ml-64"
      )}>
        {/* Top Header */}
        <header className="sticky top-0 z-30 bg-background/95 backdrop-blur-lg border-b border-border">
          <div className="flex items-center justify-between h-16 px-4 lg:px-6">
            <div className="flex items-center space-x-4">
              {isMobile && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(true)}
                >
                  <Menu className="h-4 w-4" />
                </Button>
              )}
              
              <div>
                <h1 className="text-lg font-semibold">{title}</h1>
                <p className="text-sm text-muted-foreground">
                  Panneau d'administration AfroRent
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <Button variant="ghost" size="icon">
                <Search className="h-4 w-4" />
              </Button>

              {/* Notifications */}
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-4 w-4" />
                <Badge 
                  variant="destructive" 
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
                >
                  3
                </Badge>
              </Button>

              {/* User Menu */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-semibold">
                  {profile?.prenom?.[0]}{profile?.nom?.[0]}
                </div>
                <div className="hidden md:block">
                  <p className="text-sm font-medium">
                    {profile?.prenom} {profile?.nom}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {profile?.role === 'super_admin' ? 'Super Admin' : 
                     profile?.role === 'admin' ? 'Administrateur' : 'Modérateur'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className={cn("flex-1 p-4 lg:p-6", className)}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
