import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'card' | 'list' | 'text' | 'avatar' | 'button';
  count?: number;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ 
  className, 
  variant = 'card',
  count = 1 
}) => {
  const renderSkeleton = () => {
    switch (variant) {
      case 'card':
        return (
          <div className="space-y-3">
            <div className="w-24 h-24 bg-muted animate-pulse rounded-lg" />
            <div className="space-y-2">
              <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
              <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
              <div className="h-3 bg-muted animate-pulse rounded w-2/3" />
            </div>
          </div>
        );
      
      case 'list':
        return (
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-muted animate-pulse rounded-lg flex-shrink-0" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
              <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
            </div>
          </div>
        );
      
      case 'text':
        return (
          <div className="space-y-2">
            <div className="h-4 bg-muted animate-pulse rounded w-full" />
            <div className="h-4 bg-muted animate-pulse rounded w-5/6" />
            <div className="h-4 bg-muted animate-pulse rounded w-4/6" />
          </div>
        );
      
      case 'avatar':
        return (
          <div className="w-10 h-10 bg-muted animate-pulse rounded-full" />
        );
      
      case 'button':
        return (
          <div className="h-10 bg-muted animate-pulse rounded-md w-24" />
        );
      
      default:
        return (
          <div className="h-4 bg-muted animate-pulse rounded w-full" />
        );
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className="animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
          {renderSkeleton()}
        </div>
      ))}
    </div>
  );
};

export default LoadingSkeleton;
