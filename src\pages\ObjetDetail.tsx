import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ReservationBooking from '@/components/ReservationBooking';
import ImageGallery from '@/components/ImageGallery';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Star, Calendar, Shield, ArrowLeft } from 'lucide-react';
import { ReservationFormData } from '@/types/profile';
import { formatCurrency } from '@/utils/profileValidation';

const ObjetDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Données factices pour le moment
  const objet = {
    id: id,
    titre: "Perceuse professionnelle Bosch",
    description: "Perceuse sans fil professionnelle, parfaite pour tous vos travaux de bricolage. Batterie longue durée incluse avec chargeur. Idéale pour percer le bois, le métal et la maçonnerie.",
    prix_par_jour: 5000,
    caution: 20000,
    images: [
      "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400",
      "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400",
      "https://images.unsplash.com/photo-1572981779307-38b8cabb2407?w=400"
    ],
    categorie: "Outils",
    ville: "Abidjan",
    commune: "Cocody",
    conditions: "Manipulation avec précaution. Retour dans l'état initial.",
    proprietaire: {
      nom: "KONE",
      prenom: "Moussa",
      note: 4.8,
      avis_count: 23
    },
    disponible: true
  };

  const formatPrice = (price: number) => {
    return formatCurrency(price);
  };

  const handleBackClick = () => {
    navigate(-1); // Retour à la page précédente
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header avec bouton retour */}
      <div className="sticky top-0 z-50 bg-background/95 backdrop-blur-lg border-b border-border/40">
        <div className="flex items-center justify-between px-4 py-3">
          <button
            onClick={handleBackClick}
            className="inline-flex items-center text-muted-foreground hover:text-primary transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            <span className="font-medium">Retour</span>
          </button>
          <h1 className="text-lg font-semibold truncate ml-4">Détail de l'objet</h1>
          <div className="w-16"></div> {/* Spacer pour centrer le titre */}
        </div>
      </div>

      <div className="px-4 py-6 space-y-6">

        {/* Galerie d'images */}
        <div className="mb-6">
          <ImageGallery
            images={objet.images}
            title={objet.titre}
            className="w-full rounded-lg overflow-hidden"
          />
        </div>

        {/* Informations principales */}
        <div className="space-y-4">
          {/* Badges et titre */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                {objet.categorie}
              </Badge>
              {objet.disponible && (
                <Badge variant="outline" className="text-green-700 border-green-600 bg-green-50">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Disponible
                </Badge>
              )}
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-3 leading-tight">
              {objet.titre}
            </h1>
            <div className="flex items-center text-muted-foreground">
              <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="font-medium">{objet.ville}, {objet.commune}</span>
            </div>
          </div>

          {/* Description */}
          <Card className="border-0 bg-muted/30">
            <CardContent className="p-4">
              <h3 className="font-semibold mb-2 text-foreground">Description</h3>
              <p className="text-muted-foreground leading-relaxed text-sm">
                {objet.description}
              </p>
            </CardContent>
          </Card>

          {/* Section Prix et Actions */}
          <Card className="shadow-lg border-primary/10">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Prix principal */}
                <div className="text-center border-b pb-4">
                  <div className="text-3xl font-bold text-primary mb-1">
                    {formatPrice(objet.prix_par_jour)}
                  </div>
                  <div className="text-sm text-muted-foreground font-medium">par jour</div>
                </div>

                {/* Caution */}
                {objet.caution > 0 && (
                  <div className="flex items-center justify-between bg-muted/50 rounded-lg p-3">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Shield className="h-4 w-4 mr-2" />
                      <span>Caution requise</span>
                    </div>
                    <div className="font-semibold text-foreground">
                      {formatPrice(objet.caution)}
                    </div>
                  </div>
                )}

                {/* Boutons d'action */}
                <div className="space-y-3 pt-2">
                  <Button className="w-full h-12 text-base font-semibold" size="lg">
                    <Calendar className="h-5 w-5 mr-2" />
                    Réserver cet objet
                  </Button>

                  <Button variant="outline" className="w-full h-12 text-base font-medium">
                    Contacter le propriétaire
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informations du propriétaire */}
          <Card className="border-0 bg-muted/30">
            <CardContent className="p-4">
              <h3 className="font-semibold mb-4 text-foreground">Propriétaire</h3>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-primary font-semibold text-lg">
                      {objet.proprietaire.prenom.charAt(0)}{objet.proprietaire.nom.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-foreground">
                      {objet.proprietaire.prenom} {objet.proprietaire.nom}
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Star className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{objet.proprietaire.note}</span>
                      <span className="mx-1">•</span>
                      <span>{objet.proprietaire.avis_count} avis</span>
                    </div>
                  </div>
                </div>
                <Button variant="outline" size="sm" className="text-xs">
                  Voir le profil
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Conditions de location */}
          {objet.conditions && (
            <Card className="border-0 bg-muted/30">
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3 text-foreground">Conditions de location</h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {objet.conditions}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Section Réservation */}
        <div className="mt-8 px-4">
          <ReservationBooking
            objet={{
              id: objet.id,
              titre: objet.titre,
              prix_par_jour: objet.prix_par_jour,
              images: objet.images,
              description: objet.description,
              ville: objet.ville,
              commune: objet.commune,
              owner: {
                nom: objet.proprietaire.nom,
                prenom: objet.proprietaire.prenom,
                avatar_url: undefined
              }
            }}
            onReservationSubmit={async (data: ReservationFormData) => {
              // TODO: Implement reservation submission
              console.log('Reservation data:', data);
            }}
          />
        </div>

        {/* Section Avis */}
        <div className="mt-8 px-4 pb-8">
          <Card className="border-0 bg-muted/30">
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-6 text-foreground">Avis des locataires</h3>

              <div className="text-center py-8 text-muted-foreground">
                <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="font-medium">Aucun avis pour le moment</p>
                <p className="text-sm mt-1">Soyez le premier à laisser un avis !</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ObjetDetail;