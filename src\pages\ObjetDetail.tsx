import React from 'react';
import { useParams } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import ReservationBooking from '@/components/ReservationBooking';
import ImageGallery from '@/components/ImageGallery';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Star, Calendar, Shield, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ReservationFormData } from '@/types/profile';

const ObjetDetail = () => {
  const { id } = useParams();

  // Données factices pour le moment
  const objet = {
    id: id,
    titre: "Perceuse professionnelle Bosch",
    description: "Perceuse sans fil professionnelle, parfaite pour tous vos travaux de bricolage. Batterie longue durée incluse avec chargeur. Idéale pour percer le bois, le métal et la maçonnerie.",
    prix_par_jour: 5000,
    caution: 20000,
    images: [
      "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400",
      "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400"
    ],
    categorie: "Outils",
    ville: "Abidjan",
    commune: "Cocody",
    conditions: "Manipulation avec précaution. Retour dans l'état initial.",
    proprietaire: {
      nom: "KONE",
      prenom: "Moussa",
      note: 4.8,
      avis_count: 23
    },
    disponible: true
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(price).replace('XOF', 'FCFA');
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <Link to="/explorer" className="inline-flex items-center text-muted-foreground hover:text-primary mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour aux résultats
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Images */}
          <div>
            <ImageGallery
              images={objet.images}
              title={objet.titre}
              className="w-full"
            />
          </div>

          {/* Informations */}
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="secondary">{objet.categorie}</Badge>
                {objet.disponible && (
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    Disponible
                  </Badge>
                )}
              </div>
              <h1 className="text-3xl font-bold mb-2">{objet.titre}</h1>
              <div className="flex items-center text-muted-foreground mb-4">
                <MapPin className="h-4 w-4 mr-1" />
                {objet.ville}, {objet.commune}
              </div>
            </div>

            <div className="border-b pb-4">
              <p className="text-muted-foreground leading-relaxed">
                {objet.description}
              </p>
            </div>

            {/* Prix */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="text-3xl font-bold text-primary">
                      {formatPrice(objet.prix_par_jour)}
                    </div>
                    <div className="text-sm text-muted-foreground">par jour</div>
                  </div>
                  {objet.caution > 0 && (
                    <div className="text-right">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Shield className="h-4 w-4 mr-1" />
                        Caution
                      </div>
                      <div className="font-semibold">{formatPrice(objet.caution)}</div>
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  <Button className="w-full" size="lg">
                    <Calendar className="h-4 w-4 mr-2" />
                    Réserver cet objet
                  </Button>
                  
                  <Button variant="outline" className="w-full">
                    Contacter le propriétaire
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Propriétaire */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-3">Propriétaire</h3>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">
                      {objet.proprietaire.prenom} {objet.proprietaire.nom}
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Star className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400" />
                      {objet.proprietaire.note} ({objet.proprietaire.avis_count} avis)
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    Voir le profil
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Conditions */}
            {objet.conditions && (
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-3">Conditions de location</h3>
                  <p className="text-sm text-muted-foreground">
                    {objet.conditions}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Section Réservation */}
        <div className="mt-12">
          <ReservationBooking
            objet={{
              id: objet.id,
              titre: objet.titre,
              prix_par_jour: objet.prix_par_jour,
              images: objet.images,
              description: objet.description,
              ville: objet.ville,
              commune: objet.commune,
              owner: {
                nom: objet.proprietaire.nom,
                prenom: objet.proprietaire.prenom,
                avatar_url: undefined
              }
            }}
            onReservationSubmit={async (data: ReservationFormData) => {
              // TODO: Implement reservation submission
              console.log('Reservation data:', data);
            }}
          />
        </div>

        {/* Section Avis */}
        <div className="mt-12">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-6">Avis des locataires</h3>

              <div className="text-center py-8 text-muted-foreground">
                <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Aucun avis pour le moment</p>
                <p className="text-sm">Soyez le premier à laisser un avis !</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ObjetDetail;