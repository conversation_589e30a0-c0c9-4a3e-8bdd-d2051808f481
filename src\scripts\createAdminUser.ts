// Script pour créer un utilisateur administrateur de test
// À exécuter une seule fois pour créer un compte admin

import { supabase } from '@/integrations/supabase/client';

export const createAdminUser = async () => {
  try {
    // 1. <PERSON><PERSON>er l'utilisateur dans Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'AdminAfroRent2024!',
      options: {
        data: {
          nom: 'Admin',
          prenom: 'AfroRent',
          role: 'super_admin'
        }
      }
    });

    if (authError) {
      console.error('Erreur lors de la création de l\'utilisateur auth:', authError);
      return;
    }

    console.log('Utilisateur auth créé:', authData.user?.id);

    // 2. Créer le profil admin
    if (authData.user) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          user_id: authData.user.id,
          email: '<EMAIL>',
          nom: 'Admin',
          prenom: 'AfroRent',
          role: 'super_admin',
          ville: 'Abidjan',
          commune: 'Plateau',
          telephone: '+225 07 00 00 00 00',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.error('Erreur lors de la création du profil:', profileError);
        return;
      }

      console.log('Profil admin créé:', profileData);
    }

    console.log('✅ Utilisateur admin créé avec succès !');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Mot de passe: AdminAfroRent2024!');
    console.log('🔗 URL admin: http://localhost:8081/admin');

  } catch (error) {
    console.error('Erreur générale:', error);
  }
};

// Instructions d'utilisation :
// 1. Ouvrir la console du navigateur sur http://localhost:8081
// 2. Copier et coller ce code
// 3. Exécuter createAdminUser()
// 4. Se <NAME_EMAIL> / AdminAfroRent2024!
// 5. Aller sur /admin pour accéder au panneau d'administration

console.log(`
🚀 SCRIPT DE CRÉATION D'UTILISATEUR ADMIN

Pour créer un utilisateur administrateur :
1. Ouvrez la console du navigateur (F12)
2. Exécutez : createAdminUser()
3. Connectez-vous avec :
   📧 Email: <EMAIL>
   🔑 Mot de passe: AdminAfroRent2024!
4. Accédez au panneau admin : /admin
`);
