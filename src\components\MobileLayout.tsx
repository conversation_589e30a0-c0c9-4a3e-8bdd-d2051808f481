import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import BottomNavigation from './BottomNavigation';
import MobileHeader from './MobileHeader';

interface MobileLayoutProps {
  children: ReactNode;
  className?: string;
  showBottomNav?: boolean;
  showHeader?: boolean;
  showTopPadding?: boolean;
  headerTitle?: string;
  showSearch?: boolean;
  showNotifications?: boolean;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  className,
  showBottomNav = true,
  showHeader = true,
  showTopPadding = false,
  headerTitle,
  showSearch = false,
  showNotifications = false
}) => {
  return (
    <div className="mobile-container">
      {showHeader && (
        <MobileHeader
          title={headerTitle}
          showSearch={showSearch}
          showNotifications={showNotifications}
        />
      )}
      <main
        className={cn(
          "min-h-screen",
          showBottomNav && "bottom-nav-safe",
          showTopPadding && "pt-4",
          className
        )}
      >
        {children}
      </main>
      {showBottomNav && <BottomNavigation />}
    </div>
  );
};

export default MobileLayout;
