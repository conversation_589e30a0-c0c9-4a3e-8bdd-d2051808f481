import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import BottomNavigation from './BottomNavigation';

interface MobileLayoutProps {
  children: ReactNode;
  className?: string;
  showBottomNav?: boolean;
  showTopPadding?: boolean;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({ 
  children, 
  className,
  showBottomNav = true,
  showTopPadding = true
}) => {
  return (
    <div className="mobile-container">
      <main 
        className={cn(
          "min-h-screen",
          showBottomNav && "bottom-nav-safe",
          showTopPadding && "pt-4",
          className
        )}
      >
        {children}
      </main>
      {showBottomNav && <BottomNavigation />}
    </div>
  );
};

export default MobileLayout;
