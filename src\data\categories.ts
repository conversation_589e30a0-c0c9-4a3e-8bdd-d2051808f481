// Données de catégories pour AfroRent

export interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  parent_id?: string;
  subcategories?: Category[];
}

export const OBJECT_CATEGORIES: Category[] = [
  {
    id: 'outils',
    name: 'Outils et Bricolage',
    description: 'Outils électriques, manuels et équipements de bricolage',
    icon: 'wrench',
    color: 'bg-blue-100 text-blue-900',
    subcategories: [
      {
        id: 'outils-electriques',
        name: 'Outils électriques',
        description: 'Perceuses, scies, ponceuses, etc.',
        icon: 'zap',
        color: 'bg-blue-50 text-blue-900',
        parent_id: 'outils'
      },
      {
        id: 'outils-manuels',
        name: 'Outils manuels',
        description: 'Marteaux, tournevis, clés, etc.',
        icon: 'wrench',
        color: 'bg-blue-50 text-blue-900',
        parent_id: 'outils'
      },
      {
        id: 'echelles',
        name: '<PERSON>chelles et escabeaux',
        description: 'Échelles, escabeaux, échafaudages',
        icon: 'trending-up',
        color: 'bg-blue-50 text-blue-900',
        parent_id: 'outils'
      }
    ]
  },
  {
    id: 'electromenager',
    name: 'Électroménager',
    description: 'Appareils électroménagers pour la maison',
    icon: 'home',
    color: 'bg-green-100 text-green-900',
    subcategories: [
      {
        id: 'cuisine',
        name: 'Électroménager cuisine',
        description: 'Réfrigérateurs, fours, micro-ondes, etc.',
        icon: 'chef-hat',
        color: 'bg-green-50 text-green-900',
        parent_id: 'electromenager'
      },
      {
        id: 'nettoyage',
        name: 'Nettoyage',
        description: 'Aspirateurs, nettoyeurs haute pression, etc.',
        icon: 'sparkles',
        color: 'bg-green-50 text-green-900',
        parent_id: 'electromenager'
      },
      {
        id: 'climatisation',
        name: 'Climatisation',
        description: 'Climatiseurs, ventilateurs, chauffages',
        icon: 'wind',
        color: 'bg-green-50 text-green-900',
        parent_id: 'electromenager'
      }
    ]
  },
  {
    id: 'vehicules',
    name: 'Véhicules et Transport',
    description: 'Voitures, motos, vélos et équipements de transport',
    icon: 'car',
    color: 'bg-red-100 text-red-900',
    subcategories: [
      {
        id: 'voitures',
        name: 'Voitures',
        description: 'Voitures particulières, utilitaires',
        icon: 'car',
        color: 'bg-red-50 text-red-900',
        parent_id: 'vehicules'
      },
      {
        id: 'motos',
        name: 'Motos et scooters',
        description: 'Motos, scooters, cyclomoteurs',
        icon: 'bike',
        color: 'bg-red-50 text-red-900',
        parent_id: 'vehicules'
      },
      {
        id: 'velos',
        name: 'Vélos',
        description: 'Vélos classiques, électriques, VTT',
        icon: 'bicycle',
        color: 'bg-red-50 text-red-900',
        parent_id: 'vehicules'
      }
    ]
  },
  {
    id: 'electronique',
    name: 'Électronique et High-Tech',
    description: 'Appareils électroniques, informatique, audio-vidéo',
    icon: 'smartphone',
    color: 'bg-purple-100 text-purple-900',
    subcategories: [
      {
        id: 'informatique',
        name: 'Informatique',
        description: 'Ordinateurs, tablettes, accessoires',
        icon: 'laptop',
        color: 'bg-purple-50 text-purple-900',
        parent_id: 'electronique'
      },
      {
        id: 'audio-video',
        name: 'Audio et Vidéo',
        description: 'Télévisions, chaînes hi-fi, projecteurs',
        icon: 'tv',
        color: 'bg-purple-50 text-purple-900',
        parent_id: 'electronique'
      },
      {
        id: 'photo-video',
        name: 'Photo et Vidéo',
        description: 'Appareils photo, caméras, objectifs',
        icon: 'camera',
        color: 'bg-purple-50 text-purple-900',
        parent_id: 'electronique'
      }
    ]
  },
  {
    id: 'jardinage',
    name: 'Jardinage et Extérieur',
    description: 'Outils de jardinage, mobilier extérieur, piscines',
    icon: 'flower',
    color: 'bg-emerald-100 text-emerald-900',
    subcategories: [
      {
        id: 'outils-jardinage',
        name: 'Outils de jardinage',
        description: 'Tondeuses, taille-haies, bêches, etc.',
        icon: 'scissors',
        color: 'bg-emerald-50 text-emerald-900',
        parent_id: 'jardinage'
      },
      {
        id: 'mobilier-exterieur',
        name: 'Mobilier extérieur',
        description: 'Tables, chaises, parasols, barbecues',
        icon: 'umbrella',
        color: 'bg-emerald-50 text-emerald-900',
        parent_id: 'jardinage'
      },
      {
        id: 'piscines',
        name: 'Piscines et spas',
        description: 'Piscines gonflables, spas, accessoires',
        icon: 'waves',
        color: 'bg-emerald-50 text-emerald-900',
        parent_id: 'jardinage'
      }
    ]
  },
  {
    id: 'sport',
    name: 'Sport et Loisirs',
    description: 'Équipements sportifs, jeux, loisirs créatifs',
    icon: 'dumbbell',
    color: 'bg-orange-100 text-orange-900',
    subcategories: [
      {
        id: 'fitness',
        name: 'Fitness et musculation',
        description: 'Appareils de fitness, poids, tapis de course',
        icon: 'dumbbell',
        color: 'bg-orange-50 text-orange-900',
        parent_id: 'sport'
      },
      {
        id: 'sports-collectifs',
        name: 'Sports collectifs',
        description: 'Ballons, filets, équipements de terrain',
        icon: 'users',
        color: 'bg-orange-50 text-orange-900',
        parent_id: 'sport'
      },
      {
        id: 'sports-nautiques',
        name: 'Sports nautiques',
        description: 'Planches de surf, kayaks, équipements de plongée',
        icon: 'waves',
        color: 'bg-orange-50 text-orange-900',
        parent_id: 'sport'
      }
    ]
  },
  {
    id: 'mobilier',
    name: 'Mobilier et Décoration',
    description: 'Meubles, décoration, éclairage',
    icon: 'sofa',
    color: 'bg-amber-100 text-amber-900',
    subcategories: [
      {
        id: 'meubles',
        name: 'Meubles',
        description: 'Tables, chaises, armoires, lits',
        icon: 'sofa',
        color: 'bg-amber-50 text-amber-900',
        parent_id: 'mobilier'
      },
      {
        id: 'decoration',
        name: 'Décoration',
        description: 'Tableaux, vases, objets décoratifs',
        icon: 'palette',
        color: 'bg-amber-50 text-amber-900',
        parent_id: 'mobilier'
      },
      {
        id: 'eclairage',
        name: 'Éclairage',
        description: 'Lampes, lustres, éclairage LED',
        icon: 'lightbulb',
        color: 'bg-amber-50 text-amber-900',
        parent_id: 'mobilier'
      }
    ]
  },
  {
    id: 'evenementiel',
    name: 'Événementiel',
    description: 'Matériel pour événements, fêtes, mariages',
    icon: 'party-popper',
    color: 'bg-pink-100 text-pink-900',
    subcategories: [
      {
        id: 'sonorisation',
        name: 'Sonorisation',
        description: 'Micros, enceintes, tables de mixage',
        icon: 'mic',
        color: 'bg-pink-50 text-pink-900',
        parent_id: 'evenementiel'
      },
      {
        id: 'decoration-evenement',
        name: 'Décoration événement',
        description: 'Arches, ballons, nappes, centres de table',
        icon: 'gift',
        color: 'bg-pink-50 text-pink-900',
        parent_id: 'evenementiel'
      },
      {
        id: 'mobilier-evenement',
        name: 'Mobilier événement',
        description: 'Tables, chaises, tentes, podiums',
        icon: 'tent',
        color: 'bg-pink-50 text-pink-900',
        parent_id: 'evenementiel'
      }
    ]
  },
  {
    id: 'mode',
    name: 'Mode et Accessoires',
    description: 'Vêtements, chaussures, bijoux, sacs',
    icon: 'shirt',
    color: 'bg-indigo-100 text-indigo-900',
    subcategories: [
      {
        id: 'vetements',
        name: 'Vêtements',
        description: 'Robes, costumes, vêtements traditionnels',
        icon: 'shirt',
        color: 'bg-indigo-50 text-indigo-900',
        parent_id: 'mode'
      },
      {
        id: 'chaussures',
        name: 'Chaussures',
        description: 'Chaussures de soirée, sport, traditionnelles',
        icon: 'footprints',
        color: 'bg-indigo-50 text-indigo-900',
        parent_id: 'mode'
      },
      {
        id: 'accessoires',
        name: 'Accessoires',
        description: 'Bijoux, sacs, montres, ceintures',
        icon: 'gem',
        color: 'bg-indigo-50 text-indigo-900',
        parent_id: 'mode'
      }
    ]
  },
  {
    id: 'autres',
    name: 'Autres',
    description: 'Objets divers non classés dans les autres catégories',
    icon: 'package',
    color: 'bg-gray-100 text-gray-900',
    subcategories: []
  }
];

// Helper functions
export const getCategoryById = (categoryId: string): Category | undefined => {
  // Search in main categories
  const mainCategory = OBJECT_CATEGORIES.find(cat => cat.id === categoryId);
  if (mainCategory) return mainCategory;
  
  // Search in subcategories
  for (const category of OBJECT_CATEGORIES) {
    if (category.subcategories) {
      const subCategory = category.subcategories.find(sub => sub.id === categoryId);
      if (subCategory) return subCategory;
    }
  }
  
  return undefined;
};

export const getMainCategories = (): Category[] => {
  return OBJECT_CATEGORIES;
};

export const getSubcategories = (parentId: string): Category[] => {
  const parentCategory = getCategoryById(parentId);
  return parentCategory?.subcategories || [];
};

export const getAllCategories = (): Category[] => {
  const allCategories: Category[] = [...OBJECT_CATEGORIES];
  
  OBJECT_CATEGORIES.forEach(category => {
    if (category.subcategories) {
      allCategories.push(...category.subcategories);
    }
  });
  
  return allCategories;
};

export const searchCategories = (query: string): Category[] => {
  const lowerQuery = query.toLowerCase();
  const allCategories = getAllCategories();
  
  return allCategories.filter(category => 
    category.name.toLowerCase().includes(lowerQuery) ||
    category.description.toLowerCase().includes(lowerQuery)
  );
};
