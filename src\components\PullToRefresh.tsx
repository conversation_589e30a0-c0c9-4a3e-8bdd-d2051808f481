import React, { useState, useRef, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PullToRefreshProps {
  children: React.ReactNode;
  onRefresh: () => Promise<void>;
  disabled?: boolean;
}

const PullToRefresh: React.FC<PullToRefreshProps> = ({ 
  children, 
  onRefresh, 
  disabled = false 
}) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [startY, setStartY] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const threshold = 80;
  const maxPull = 120;

  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled || isRefreshing) return;
    
    const container = containerRef.current;
    if (!container || container.scrollTop > 0) return;
    
    setStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (disabled || isRefreshing || startY === 0) return;
    
    const container = containerRef.current;
    if (!container || container.scrollTop > 0) return;
    
    const currentY = e.touches[0].clientY;
    const distance = Math.max(0, currentY - startY);
    
    if (distance > 0) {
      e.preventDefault();
      setPullDistance(Math.min(distance * 0.5, maxPull));
    }
  };

  const handleTouchEnd = async () => {
    if (disabled || isRefreshing || pullDistance < threshold) {
      setPullDistance(0);
      setStartY(0);
      return;
    }

    setIsRefreshing(true);
    setPullDistance(threshold);
    
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
      setPullDistance(0);
      setStartY(0);
    }
  };

  const refreshProgress = Math.min(pullDistance / threshold, 1);
  const rotation = refreshProgress * 180;

  return (
    <div 
      ref={containerRef}
      className="relative overflow-hidden"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull to refresh indicator */}
      <div 
        className={cn(
          "absolute top-0 left-0 right-0 flex items-center justify-center transition-all duration-300 z-10",
          "bg-gradient-to-b from-background to-background/80 backdrop-blur-sm"
        )}
        style={{ 
          height: `${pullDistance}px`,
          transform: `translateY(-${Math.max(0, threshold - pullDistance)}px)`
        }}
      >
        <div className="flex flex-col items-center space-y-2">
          <div 
            className={cn(
              "transition-all duration-300",
              isRefreshing && "animate-spin"
            )}
            style={{ 
              transform: `rotate(${isRefreshing ? 0 : rotation}deg)`,
              opacity: Math.max(0.3, refreshProgress)
            }}
          >
            <RefreshCw 
              className={cn(
                "h-6 w-6 transition-colors duration-300",
                refreshProgress >= 1 ? "text-primary" : "text-muted-foreground"
              )} 
            />
          </div>
          {pullDistance > 20 && (
            <span 
              className={cn(
                "text-xs font-medium transition-all duration-300",
                refreshProgress >= 1 ? "text-primary" : "text-muted-foreground"
              )}
              style={{ opacity: Math.max(0, refreshProgress - 0.2) }}
            >
              {isRefreshing 
                ? "Actualisation..." 
                : refreshProgress >= 1 
                  ? "Relâchez pour actualiser" 
                  : "Tirez pour actualiser"
              }
            </span>
          )}
        </div>
      </div>

      {/* Content */}
      <div 
        style={{ 
          transform: `translateY(${pullDistance}px)`,
          transition: isRefreshing || pullDistance === 0 ? 'transform 0.3s ease-out' : 'none'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default PullToRefresh;
