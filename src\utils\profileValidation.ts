import { ProfileFormData, ProfileValidationErrors, ReservationFormData, ReservationValidationErrors } from '@/types/profile';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Ivorian phone number regex (+225 XX XX XX XX XX)
const PHONE_REGEX = /^\+225\s\d{2}\s\d{2}\s\d{2}\s\d{2}\s\d{2}$/;

export const validateProfileForm = (data: ProfileFormData): ProfileValidationErrors => {
  const errors: ProfileValidationErrors = {};

  // Validate nom (required, min 2 characters)
  if (!data.nom || data.nom.trim().length === 0) {
    errors.nom = 'Le nom est requis';
  } else if (data.nom.trim().length < 2) {
    errors.nom = 'Le nom doit contenir au moins 2 caractères';
  } else if (data.nom.trim().length > 50) {
    errors.nom = 'Le nom ne peut pas dépasser 50 caractères';
  }

  // Validate prenom (required, min 2 characters)
  if (!data.prenom || data.prenom.trim().length === 0) {
    errors.prenom = 'Le prénom est requis';
  } else if (data.prenom.trim().length < 2) {
    errors.prenom = 'Le prénom doit contenir au moins 2 caractères';
  } else if (data.prenom.trim().length > 50) {
    errors.prenom = 'Le prénom ne peut pas dépasser 50 caractères';
  }

  // Validate email (required, proper format)
  if (!data.email || data.email.trim().length === 0) {
    errors.email = 'L\'adresse email est requise';
  } else if (!EMAIL_REGEX.test(data.email.trim())) {
    errors.email = 'Veuillez saisir une adresse email valide';
  } else if (data.email.trim().length > 100) {
    errors.email = 'L\'adresse email ne peut pas dépasser 100 caractères';
  }

  // Validate telephone (optional, but if provided must match Ivorian format)
  if (data.telephone && data.telephone.trim().length > 0) {
    if (!PHONE_REGEX.test(data.telephone.trim())) {
      errors.telephone = 'Le numéro doit être au format +225 XX XX XX XX XX';
    }
  }

  // Validate ville (required)
  if (!data.ville || data.ville.trim().length === 0) {
    errors.ville = 'La ville est requise';
  }

  // Validate role (required)
  if (!data.role || (data.role !== 'locataire' && data.role !== 'loueur')) {
    errors.role = 'Veuillez sélectionner un rôle valide';
  }

  return errors;
};

export const validateReservationForm = (data: ReservationFormData): ReservationValidationErrors => {
  const errors: ReservationValidationErrors = {};

  // Validate start_date (required, not in the past)
  if (!data.start_date) {
    errors.start_date = 'La date de début est requise';
  } else {
    const startDate = new Date(data.start_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (startDate < today) {
      errors.start_date = 'La date de début ne peut pas être dans le passé';
    }
  }

  // Validate end_date (required, after start_date)
  if (!data.end_date) {
    errors.end_date = 'La date de fin est requise';
  } else if (data.start_date) {
    const startDate = new Date(data.start_date);
    const endDate = new Date(data.end_date);
    
    if (endDate <= startDate) {
      errors.end_date = 'La date de fin doit être après la date de début';
    }
    
    // Check if reservation is too long (max 30 days)
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 30) {
      errors.end_date = 'La durée de location ne peut pas dépasser 30 jours';
    }
  }

  // Validate message (optional, but if provided max 500 characters)
  if (data.message && data.message.trim().length > 500) {
    errors.message = 'Le message ne peut pas dépasser 500 caractères';
  }

  return errors;
};

export const hasValidationErrors = (errors: ProfileValidationErrors | ReservationValidationErrors): boolean => {
  return Object.keys(errors).length > 0;
};

export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // If it starts with +225, format it properly
  if (cleaned.startsWith('+225')) {
    const digits = cleaned.substring(4);
    if (digits.length === 10) {
      return `+225 ${digits.substring(0, 2)} ${digits.substring(2, 4)} ${digits.substring(4, 6)} ${digits.substring(6, 8)} ${digits.substring(8, 10)}`;
    }
  }
  
  // If it's just 10 digits, add +225 prefix
  if (cleaned.length === 10 && !cleaned.startsWith('+')) {
    return `+225 ${cleaned.substring(0, 2)} ${cleaned.substring(2, 4)} ${cleaned.substring(4, 6)} ${cleaned.substring(6, 8)} ${cleaned.substring(8, 10)}`;
  }
  
  return phone;
};

export const validateEmail = async (email: string, currentUserEmail?: string): Promise<boolean> => {
  // Basic format validation
  if (!EMAIL_REGEX.test(email)) {
    return false;
  }
  
  // If it's the same as current user's email, it's valid
  if (currentUserEmail && email === currentUserEmail) {
    return true;
  }
  
  // TODO: Implement uniqueness check with Supabase
  // This would involve checking if the email already exists in the database
  // For now, we'll assume it's unique if format is correct
  return true;
};

export const sanitizeProfileData = (data: ProfileFormData): ProfileFormData => {
  return {
    nom: data.nom.trim(),
    prenom: data.prenom.trim(),
    email: data.email.trim().toLowerCase(),
    telephone: data.telephone ? formatPhoneNumber(data.telephone) : '',
    ville: data.ville.trim(),
    commune: data.commune.trim(),
    role: data.role
  };
};

export const calculateReservationDays = (startDate: string, endDate: string): number => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const calculateTotalPrice = (dailyPrice: number, startDate: string, endDate: string): number => {
  const days = calculateReservationDays(startDate, endDate);
  return dailyPrice * days;
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fr-CI', {
    style: 'currency',
    currency: 'XOF',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export const formatDate = (dateString: string): string => {
  return new Intl.DateTimeFormat('fr-CI', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(dateString));
};

export const formatDateShort = (dateString: string): string => {
  return new Intl.DateTimeFormat('fr-CI', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(new Date(dateString));
};
