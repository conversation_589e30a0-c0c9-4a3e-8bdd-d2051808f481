import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Objet {
  id: string;
  titre: string;
  description: string;
  prix_par_jour: number;
  caution: number;
  proprietaire_id: string;
  categorie_id?: string;
  localisation_ville: string;
  localisation_commune?: string;
  images: string[];
  disponible: boolean;
  conditions?: string;
  created_at: string;
  updated_at: string;
  profiles?: {
    nom?: string;
    prenom?: string;
    ville: string;
    commune?: string;
  };
  categories?: {
    nom: string;
    icone?: string;
  };
}

export const useObjects = (filters?: {
  categorie?: string;
  ville?: string;
  prixMax?: number;
  search?: string;
}) => {
  const [objects, setObjects] = useState<Objet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchObjects();
  }, [filters]);

  const fetchObjects = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('objets')
        .select(`
          *,
          profiles:proprietaire_id(nom, prenom, ville, commune),
          categories:categorie_id(nom, icone)
        `)
        .eq('disponible', true)
        .order('created_at', { ascending: false });

      // Appliquer les filtres
      if (filters?.categorie) {
        query = query.eq('categorie_id', filters.categorie);
      }
      
      if (filters?.ville) {
        query = query.eq('localisation_ville', filters.ville);
      }
      
      if (filters?.prixMax) {
        query = query.lte('prix_par_jour', filters.prixMax);
      }
      
      if (filters?.search) {
        query = query.or(`titre.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;

      if (error) {
        setError(error.message);
        return;
      }

      setObjects(data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des objets');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return { objects, loading, error, refetch: fetchObjects };
};