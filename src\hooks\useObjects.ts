import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Objet {
  id: string;
  titre: string;
  description: string;
  prix_par_jour: number;
  caution: number;
  proprietaire_id: string;
  categorie_id?: string;
  localisation_ville: string;
  localisation_commune?: string;
  images: string[];
  disponible: boolean;
  conditions?: string;
  created_at: string;
  updated_at: string;
  profiles?: {
    nom?: string;
    prenom?: string;
    ville: string;
    commune?: string;
  };
  categories?: {
    nom: string;
    icone?: string;
  };
}

export const useObjects = (filters?: {
  categorie?: string;
  ville?: string;
  commune?: string;
  prixMin?: number;
  prixMax?: number;
  search?: string;
  dateDebut?: string;
  dateFin?: string;
  sortBy?: string;
}) => {
  const [objects, setObjects] = useState<Objet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchObjects();
  }, [filters]);

  const fetchObjects = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('objets')
        .select(`
          *,
          profiles:proprietaire_id(nom, prenom, ville, commune),
          categories:categorie_id(nom, icone)
        `)
        .eq('disponible', true)
        .order('created_at', { ascending: false });

      // Appliquer les filtres
      if (filters?.categorie) {
        // Support pour catégories multiples séparées par des virgules
        const categories = filters.categorie.split(',').filter(Boolean);
        if (categories.length === 1) {
          query = query.eq('categorie_id', categories[0]);
        } else if (categories.length > 1) {
          query = query.in('categorie_id', categories);
        }
      }

      if (filters?.ville) {
        query = query.eq('localisation_ville', filters.ville);
      }

      if (filters?.commune) {
        query = query.eq('localisation_commune', filters.commune);
      }

      if (filters?.prixMin && filters.prixMin > 0) {
        query = query.gte('prix_par_jour', filters.prixMin);
      }

      if (filters?.prixMax && filters.prixMax < 100000) {
        query = query.lte('prix_par_jour', filters.prixMax);
      }

      if (filters?.search) {
        query = query.or(`titre.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      // TODO: Implémenter le filtrage par dates de disponibilité
      // if (filters?.dateDebut) {
      //   // Logique pour vérifier la disponibilité
      // }

      // Appliquer le tri
      if (filters?.sortBy) {
        switch (filters.sortBy) {
          case 'price_asc':
            query = query.order('prix_par_jour', { ascending: true });
            break;
          case 'price_desc':
            query = query.order('prix_par_jour', { ascending: false });
            break;
          case 'date_asc':
            query = query.order('created_at', { ascending: true });
            break;
          case 'date_desc':
          default:
            query = query.order('created_at', { ascending: false });
            break;
          // TODO: Implémenter tri par popularité et distance
        }
      }

      const { data, error } = await query;

      if (error) {
        setError(error.message);
        return;
      }

      setObjects(data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des objets');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return { objects, loading, error, refetch: fetchObjects };
};