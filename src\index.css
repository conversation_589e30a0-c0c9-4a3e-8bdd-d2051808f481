@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    /* Professional blue-based primary colors */
    --primary: 210 85% 45%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 210 85% 55%;
    --primary-light: 210 85% 65%;

    /* Warm gray secondary colors */
    --secondary: 210 15% 96%;
    --secondary-foreground: 210 25% 25%;

    /* Muted warm grays */
    --muted: 210 15% 96%;
    --muted-foreground: 215 15% 45%;

    /* Subtle accent colors */
    --accent: 210 20% 94%;
    --accent-foreground: 210 25% 25%;

    /* Status colors - more muted */
    --success: 145 65% 42%;
    --success-foreground: 0 0% 98%;

    --warning: 35 85% 55%;
    --warning-foreground: 0 0% 98%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 210 40% 98%;

    /* Borders and inputs */
    --border: 210 20% 88%;
    --input: 210 20% 88%;
    --ring: 210 85% 45%;

    /* Custom design tokens with new color scheme */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-hero: linear-gradient(135deg, hsl(210 85% 45%), hsl(210 85% 55%));
    --gradient-accent: linear-gradient(135deg, hsl(210 85% 50%), hsl(195 85% 55%));
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.25);
    --shadow-card: 0 4px 20px -5px hsl(215 25% 25% / 0.12);
    --shadow-bottom-nav: 0 -4px 20px -5px hsl(215 25% 25% / 0.08);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 215 25% 8%;
    --foreground: 210 15% 92%;

    --card: 215 25% 10%;
    --card-foreground: 210 15% 92%;

    --popover: 215 25% 10%;
    --popover-foreground: 210 15% 92%;

    /* Dark theme primary colors */
    --primary: 210 85% 55%;
    --primary-foreground: 215 25% 8%;
    --primary-glow: 210 85% 65%;
    --primary-light: 210 85% 75%;

    --secondary: 215 20% 15%;
    --secondary-foreground: 210 15% 85%;

    --muted: 215 20% 15%;
    --muted-foreground: 215 15% 65%;

    --accent: 215 20% 18%;
    --accent-foreground: 210 15% 85%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 210 15% 92%;

    --border: 215 20% 18%;
    --input: 215 20% 18%;
    --ring: 210 85% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Mobile-first responsive design */
  html {
    @apply scroll-smooth;
  }

  /* Hide scrollbar for mobile app feel */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Mobile app container */
  .mobile-container {
    @apply max-w-md mx-auto bg-background min-h-screen relative;
  }

  /* Bottom navigation safe area */
  .bottom-nav-safe {
    @apply pb-20;
  }

  /* Enhanced animations for mobile app feel */
  .animate-bounce-in {
    animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .animate-slide-in-left {
    animation: slide-in-left 0.4s ease-out;
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.4s ease-out;
  }

  .animate-pulse-soft {
    animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes bounce-in {
    0% {
      opacity: 0;
      transform: scale(0.3) translateY(20px);
    }
    50% {
      opacity: 1;
      transform: scale(1.05) translateY(-5px);
    }
    70% {
      transform: scale(0.9) translateY(0);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes slide-in-left {
    0% {
      opacity: 0;
      transform: translateX(-30px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slide-in-right {
    0% {
      opacity: 0;
      transform: translateX(30px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulse-soft {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  /* Touch feedback */
  .touch-feedback {
    transition: all 0.1s ease;
  }

  .touch-feedback:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}