import { useState, useEffect } from 'react';
import { MockService } from '@/data/mockData/mockService';
import { MockCategorie } from '@/data/mockData/types';

// Utilisation du type MockCategorie
export type Categorie = MockCategorie;

export const useCategories = () => {
  const [categories, setCategories] = useState<Categorie[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);

      // Utiliser le service mock
      const data = await MockService.getCategories();

      // Trier par ordre puis par nom
      const sortedCategories = data.sort((a, b) => {
        if (a.ordre !== b.ordre) {
          return a.ordre - b.ordre;
        }
        return a.nom.localeCompare(b.nom);
      });

      setCategories(sortedCategories);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des catégories');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return { categories, loading, error, refetch: fetchCategories };
};