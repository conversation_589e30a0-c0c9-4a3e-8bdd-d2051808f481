import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Categorie {
  id: string;
  nom: string;
  description?: string;
  icone?: string;
  created_at: string;
}

export const useCategories = () => {
  const [categories, setCategories] = useState<Categorie[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('nom');

      if (error) {
        setError(error.message);
        return;
      }

      setCategories(data || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des catégories');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return { categories, loading, error, refetch: fetchCategories };
};