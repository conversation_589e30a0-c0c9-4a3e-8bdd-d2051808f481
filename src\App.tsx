import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import MobileLayout from "@/components/MobileLayout";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import Explorer from "./pages/Explorer";
import Dashboard from "./pages/Dashboard";
import ObjetDetail from "./pages/ObjetDetail";
import ProfileEdit from "./components/ProfileEdit";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={
              <MobileLayout showNotifications={true}>
                <Index />
              </MobileLayout>
            } />
            <Route path="/auth" element={
              <MobileLayout showHeader={false} showBottomNav={false}>
                <Auth />
              </MobileLayout>
            } />
            <Route path="/explorer" element={
              <MobileLayout showSearch={true}>
                <Explorer />
              </MobileLayout>
            } />
            <Route path="/dashboard" element={
              <MobileLayout showNotifications={true}>
                <Dashboard />
              </MobileLayout>
            } />
            <Route path="/objet/:id" element={
              <MobileLayout>
                <ObjetDetail />
              </MobileLayout>
            } />
            <Route path="/profile/edit" element={
              <MobileLayout headerTitle="Modifier le profil">
                <ProfileEdit />
              </MobileLayout>
            } />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={
              <MobileLayout>
                <NotFound />
              </MobileLayout>
            } />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
