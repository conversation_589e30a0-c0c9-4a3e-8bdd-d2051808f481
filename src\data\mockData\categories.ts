import { MockCategorie } from './types';

export const MOCK_CATEGORIES: MockCategorie[] = [
  // Catégories principales
  {
    id: 'outils-bricolage',
    nom: 'Outils & Bricolage',
    description: 'Perceuses, marteaux, scies et tout l\'équipement pour vos travaux',
    icone: 'wrench',
    color: 'text-blue-900',
    ordre: 1,
    nombre_objets: 45,
    image_url: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400'
  },
  {
    id: 'electromenager',
    nom: 'Électroménager',
    description: 'Réfrigérateurs, lave-linge, micro-ondes et appareils de cuisine',
    icone: 'zap',
    color: 'text-green-900',
    ordre: 2,
    nombre_objets: 38,
    image_url: 'https://images.unsplash.com/photo-1571175443880-49e1d25b2bc5?w=400'
  },
  {
    id: 'electronique',
    nom: 'Électronique',
    description: 'Smartphones, ordinateurs, appareils photo et gadgets tech',
    icone: 'smartphone',
    color: 'text-purple-900',
    ordre: 3,
    nombre_objets: 52,
    image_url: 'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=400'
  },
  {
    id: 'mobilier',
    nom: 'Mobilier & Décoration',
    description: 'Tables, chaises, canapés et éléments de décoration',
    icone: 'home',
    color: 'text-amber-900',
    ordre: 4,
    nombre_objets: 67,
    image_url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400'
  },
  {
    id: 'vehicules',
    nom: 'Véhicules & Transport',
    description: 'Voitures, motos, vélos et moyens de transport',
    icone: 'car',
    color: 'text-red-900',
    ordre: 5,
    nombre_objets: 29,
    image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'
  },
  {
    id: 'sport-loisirs',
    nom: 'Sport & Loisirs',
    description: 'Équipements sportifs, jeux et matériel de loisirs',
    icone: 'activity',
    color: 'text-emerald-900',
    ordre: 6,
    nombre_objets: 41,
    image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400'
  },
  {
    id: 'jardinage',
    nom: 'Jardinage & Extérieur',
    description: 'Tondeuses, outils de jardinage et équipements d\'extérieur',
    icone: 'flower',
    color: 'text-green-900',
    ordre: 7,
    nombre_objets: 33,
    image_url: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400'
  },
  {
    id: 'evenementiel',
    nom: 'Événementiel',
    description: 'Matériel pour fêtes, mariages et événements spéciaux',
    icone: 'music',
    color: 'text-pink-900',
    ordre: 8,
    nombre_objets: 24,
    image_url: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=400'
  },
  {
    id: 'vetements-mode',
    nom: 'Vêtements & Mode',
    description: 'Robes de soirée, costumes et accessoires de mode',
    icone: 'shirt',
    color: 'text-indigo-900',
    ordre: 9,
    nombre_objets: 19,
    image_url: 'https://images.unsplash.com/photo-1566479179817-c0b5b4b4b1e5?w=400'
  },
  {
    id: 'bebe-enfant',
    nom: 'Bébé & Enfant',
    description: 'Poussettes, sièges auto et équipements pour enfants',
    icone: 'baby',
    color: 'text-yellow-900',
    ordre: 10,
    nombre_objets: 16,
    image_url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400'
  },

  // Sous-catégories pour Outils & Bricolage
  {
    id: 'outils-electriques',
    nom: 'Outils électriques',
    description: 'Perceuses, scies électriques, ponceuses',
    icone: 'zap',
    color: 'text-blue-800',
    parent_id: 'outils-bricolage',
    ordre: 11,
    nombre_objets: 25
  },
  {
    id: 'outils-manuels',
    nom: 'Outils manuels',
    description: 'Marteaux, tournevis, clés et outils à main',
    icone: 'wrench',
    color: 'text-blue-800',
    parent_id: 'outils-bricolage',
    ordre: 12,
    nombre_objets: 20
  },

  // Sous-catégories pour Électroménager
  {
    id: 'cuisine',
    nom: 'Cuisine',
    description: 'Réfrigérateurs, fours, micro-ondes',
    icone: 'chef-hat',
    color: 'text-green-800',
    parent_id: 'electromenager',
    ordre: 13,
    nombre_objets: 22
  },
  {
    id: 'lavage',
    nom: 'Lavage & Nettoyage',
    description: 'Lave-linge, lave-vaisselle, aspirateurs',
    icone: 'droplets',
    color: 'text-green-800',
    parent_id: 'electromenager',
    ordre: 14,
    nombre_objets: 16
  },

  // Sous-catégories pour Électronique
  {
    id: 'photo-video',
    nom: 'Photo & Vidéo',
    description: 'Appareils photo, caméras, objectifs',
    icone: 'camera',
    color: 'text-purple-800',
    parent_id: 'electronique',
    ordre: 15,
    nombre_objets: 18
  },
  {
    id: 'informatique',
    nom: 'Informatique',
    description: 'Ordinateurs, tablettes, accessoires',
    icone: 'laptop',
    color: 'text-purple-800',
    parent_id: 'electronique',
    ordre: 16,
    nombre_objets: 21
  },
  {
    id: 'audio',
    nom: 'Audio & Son',
    description: 'Enceintes, casques, matériel audio',
    icone: 'headphones',
    color: 'text-purple-800',
    parent_id: 'electronique',
    ordre: 17,
    nombre_objets: 13
  },

  // Sous-catégories pour Véhicules
  {
    id: 'velos',
    nom: 'Vélos',
    description: 'Vélos classiques, électriques, VTT',
    icone: 'bike',
    color: 'text-red-800',
    parent_id: 'vehicules',
    ordre: 18,
    nombre_objets: 15
  },
  {
    id: 'motos',
    nom: 'Motos & Scooters',
    description: 'Motos, scooters et deux-roues motorisés',
    icone: 'bike',
    color: 'text-red-800',
    parent_id: 'vehicules',
    ordre: 19,
    nombre_objets: 8
  },
  {
    id: 'voitures',
    nom: 'Voitures',
    description: 'Voitures particulières et utilitaires',
    icone: 'car',
    color: 'text-red-800',
    parent_id: 'vehicules',
    ordre: 20,
    nombre_objets: 6
  },

  // Sous-catégories pour Sport & Loisirs
  {
    id: 'fitness',
    nom: 'Fitness & Musculation',
    description: 'Tapis de course, haltères, équipements de gym',
    icone: 'dumbbell',
    color: 'text-emerald-800',
    parent_id: 'sport-loisirs',
    ordre: 21,
    nombre_objets: 18
  },
  {
    id: 'sports-aquatiques',
    nom: 'Sports aquatiques',
    description: 'Planches de surf, kayaks, équipements de plongée',
    icone: 'waves',
    color: 'text-emerald-800',
    parent_id: 'sport-loisirs',
    ordre: 22,
    nombre_objets: 12
  },
  {
    id: 'jeux',
    nom: 'Jeux & Divertissement',
    description: 'Consoles de jeux, jeux de société, divertissement',
    icone: 'gamepad',
    color: 'text-emerald-800',
    parent_id: 'sport-loisirs',
    ordre: 23,
    nombre_objets: 11
  }
];

// Fonction pour obtenir les catégories principales
export const getMainCategories = (): MockCategorie[] => {
  return MOCK_CATEGORIES.filter(cat => !cat.parent_id);
};

// Fonction pour obtenir les sous-catégories d'une catégorie
export const getSubCategories = (parentId: string): MockCategorie[] => {
  return MOCK_CATEGORIES.filter(cat => cat.parent_id === parentId);
};

// Fonction pour obtenir une catégorie par ID
export const getCategoryById = (id: string): MockCategorie | undefined => {
  return MOCK_CATEGORIES.find(cat => cat.id === id);
};

// Fonction pour rechercher des catégories
export const searchCategories = (query: string): MockCategorie[] => {
  const searchTerm = query.toLowerCase();
  return MOCK_CATEGORIES.filter(cat => 
    cat.nom.toLowerCase().includes(searchTerm) ||
    cat.description.toLowerCase().includes(searchTerm)
  );
};

// Fonction pour obtenir les catégories les plus populaires
export const getPopularCategories = (limit: number = 6): MockCategorie[] => {
  return MOCK_CATEGORIES
    .filter(cat => !cat.parent_id)
    .sort((a, b) => b.nombre_objets - a.nombre_objets)
    .slice(0, limit);
};

export default MOCK_CATEGORIES;
