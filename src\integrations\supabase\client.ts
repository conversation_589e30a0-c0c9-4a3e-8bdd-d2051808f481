// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dwtlffshkmpgzqeiaisr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR3dGxmZnNoa21wZ3pxZWlhaXNyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0OTk2NjEsImV4cCI6MjA2ODA3NTY2MX0.tdwKhC77W2-sSA0cV3Lmlc4eoceBiKQCnPLIabrZsnQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});