import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>ckCate<PERSON><PERSON>, 
  <PERSON>ck<PERSON><PERSON><PERSON>, 
  MockSearchResult,
  MockConfig,
  MockStats
} from './types';
import { MOCK_USERS, getUserById, getLoueurs } from './users';
import { MOCK_CATEGORIES, getMainCategories, getCategoryById } from './categories';
import { MOCK_OBJECTS, getObjectById, getAvailableObjects, getFeaturedObjects } from './objects';

// Configuration du service mock
const MOCK_CONFIG: MockConfig = {
  enabled: true,
  delay_simulation: 300, // 300ms de délai pour simuler le réseau
  error_rate: 0.02, // 2% de chance d'erreur
  cache_enabled: true,
  log_requests: true
};

// Cache simple pour les requêtes
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

// Fonction utilitaire pour simuler un délai réseau
const simulateNetworkDelay = (): Promise<void> => {
  return new Promise(resolve => {
    setTimeout(resolve, MOCK_CONFIG.delay_simulation);
  });
};

// Fonction utilitaire pour simuler des erreurs occasionnelles
const simulateRandomError = (): void => {
  if (Math.random() < MOCK_CONFIG.error_rate) {
    throw new Error('Erreur réseau simulée');
  }
};

// Fonction utilitaire pour le cache
const getCacheKey = (method: string, params: any): string => {
  return `${method}_${JSON.stringify(params)}`;
};

const getFromCache = (key: string): any | null => {
  if (!MOCK_CONFIG.cache_enabled) return null;
  
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.data;
  }
  
  cache.delete(key);
  return null;
};

const setCache = (key: string, data: any, ttl: number = 60000): void => {
  if (!MOCK_CONFIG.cache_enabled) return;
  
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  });
};

// Fonction de logging
const logRequest = (method: string, params: any, result: any): void => {
  if (MOCK_CONFIG.log_requests) {
    console.log(`[MockService] ${method}`, { params, resultCount: Array.isArray(result) ? result.length : 1 });
  }
};

// Service Mock Principal
export class MockService {
  
  // === AUTHENTIFICATION ===
  
  static async signIn(email: string, password: string): Promise<{ user: MockUser | null; error: string | null }> {
    await simulateNetworkDelay();
    simulateRandomError();
    
    // Authentification simplifiée pour les tests
    const user = MOCK_USERS.find(u => u.email === email);
    
    if (!user) {
      return { user: null, error: 'Utilisateur non trouvé' };
    }
    
    // Pour les tests, accepter n'importe quel mot de passe
    if (password.length < 3) {
      return { user: null, error: 'Mot de passe trop court' };
    }
    
    logRequest('signIn', { email }, user);
    return { user, error: null };
  }
  
  static async signUp(email: string, password: string, userData: Partial<MockUser>): Promise<{ user: MockUser | null; error: string | null }> {
    await simulateNetworkDelay();
    simulateRandomError();
    
    // Vérifier si l'email existe déjà
    const existingUser = MOCK_USERS.find(u => u.email === email);
    if (existingUser) {
      return { user: null, error: 'Cet email est déjà utilisé' };
    }
    
    // Créer un nouvel utilisateur
    const newUser: MockUser = {
      id: `user-${Date.now()}`,
      user_id: `user-${Date.now()}`,
      email,
      nom: userData.nom || 'Nom',
      prenom: userData.prenom || 'Prénom',
      telephone: userData.telephone,
      ville: userData.ville || 'Abidjan',
      commune: userData.commune,
      role: userData.role || 'locataire',
      avatar_url: userData.avatar_url,
      note_moyenne: 5.0,
      nombre_avis: 0,
      date_inscription: new Date().toISOString(),
      derniere_connexion: new Date().toISOString(),
      verified: false,
      bio: userData.bio
    };
    
    MOCK_USERS.push(newUser);
    logRequest('signUp', { email, userData }, newUser);
    return { user: newUser, error: null };
  }
  
  static async getCurrentUser(userId: string): Promise<MockUser | null> {
    await simulateNetworkDelay();
    const user = getUserById(userId);
    logRequest('getCurrentUser', { userId }, user);
    return user || null;
  }
  
  // === OBJETS ===
  
  static async getObjects(filters: MockFilters = {}): Promise<MockSearchResult> {
    await simulateNetworkDelay();
    simulateRandomError();
    
    const cacheKey = getCacheKey('getObjects', filters);
    const cached = getFromCache(cacheKey);
    if (cached) {
      logRequest('getObjects', filters, cached);
      return cached;
    }
    
    let objects = [...MOCK_OBJECTS];
    
    // Appliquer les filtres
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      objects = objects.filter(obj => 
        obj.titre.toLowerCase().includes(searchTerm) ||
        obj.description.toLowerCase().includes(searchTerm) ||
        obj.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }
    
    if (filters.categorie) {
      objects = objects.filter(obj => obj.categorie_id === filters.categorie);
    }
    
    if (filters.ville) {
      objects = objects.filter(obj => obj.localisation_ville === filters.ville);
    }
    
    if (filters.commune) {
      objects = objects.filter(obj => obj.localisation_commune === filters.commune);
    }
    
    if (filters.prix_min !== undefined) {
      objects = objects.filter(obj => obj.prix_par_jour >= filters.prix_min!);
    }
    
    if (filters.prix_max !== undefined) {
      objects = objects.filter(obj => obj.prix_par_jour <= filters.prix_max!);
    }
    
    if (filters.disponible_seulement) {
      objects = objects.filter(obj => obj.disponible);
    }
    
    if (filters.livraison_possible) {
      objects = objects.filter(obj => obj.livraison_possible);
    }
    
    if (filters.note_min !== undefined) {
      objects = objects.filter(obj => obj.note_moyenne >= filters.note_min!);
    }
    
    if (filters.etat && filters.etat.length > 0) {
      objects = objects.filter(obj => filters.etat!.includes(obj.etat));
    }
    
    if (filters.tags && filters.tags.length > 0) {
      objects = objects.filter(obj => 
        filters.tags!.some(tag => obj.tags.includes(tag))
      );
    }
    
    // Tri par pertinence (pour l'instant, par popularité)
    objects.sort((a, b) => (b.vues + b.favoris) - (a.vues + a.favoris));
    
    const result: MockSearchResult = {
      objets: objects,
      total: objects.length,
      page: 1,
      pages_total: 1,
      filtres_appliques: filters
    };
    
    setCache(cacheKey, result, 30000); // Cache 30 secondes
    logRequest('getObjects', filters, result);
    return result;
  }
  
  static async getObjectById(id: string): Promise<MockObjet | null> {
    await simulateNetworkDelay();
    const object = getObjectById(id);
    logRequest('getObjectById', { id }, object);
    return object || null;
  }
  
  static async getFeaturedObjects(limit: number = 6): Promise<MockObjet[]> {
    await simulateNetworkDelay();
    const objects = getFeaturedObjects(limit);
    logRequest('getFeaturedObjects', { limit }, objects);
    return objects;
  }
  
  // === CATÉGORIES ===
  
  static async getCategories(): Promise<MockCategorie[]> {
    await simulateNetworkDelay();
    
    const cacheKey = getCacheKey('getCategories', {});
    const cached = getFromCache(cacheKey);
    if (cached) {
      logRequest('getCategories', {}, cached);
      return cached;
    }
    
    const categories = [...MOCK_CATEGORIES];
    setCache(cacheKey, categories, 300000); // Cache 5 minutes
    logRequest('getCategories', {}, categories);
    return categories;
  }
  
  static async getMainCategories(): Promise<MockCategorie[]> {
    await simulateNetworkDelay();
    const categories = getMainCategories();
    logRequest('getMainCategories', {}, categories);
    return categories;
  }
  
  static async getCategoryById(id: string): Promise<MockCategorie | null> {
    await simulateNetworkDelay();
    const category = getCategoryById(id);
    logRequest('getCategoryById', { id }, category);
    return category || null;
  }
  
  // === UTILISATEURS ===
  
  static async getUsers(): Promise<MockUser[]> {
    await simulateNetworkDelay();
    logRequest('getUsers', {}, MOCK_USERS);
    return [...MOCK_USERS];
  }
  
  static async getUserById(id: string): Promise<MockUser | null> {
    await simulateNetworkDelay();
    const user = getUserById(id);
    logRequest('getUserById', { id }, user);
    return user || null;
  }
  
  // === STATISTIQUES ===
  
  static async getStats(): Promise<MockStats> {
    await simulateNetworkDelay();
    
    const stats: MockStats = {
      total_objets: MOCK_OBJECTS.length,
      total_utilisateurs: MOCK_USERS.length,
      total_reservations: 156,
      revenus_total: 2450000,
      objets_populaires: getFeaturedObjects(5),
      utilisateurs_actifs: getLoueurs().slice(0, 5),
      reservations_recentes: [],
      croissance_mensuelle: [
        { mois: 'Jan', objets: 45, utilisateurs: 23, reservations: 12, revenus: 180000 },
        { mois: 'Fév', objets: 52, utilisateurs: 31, reservations: 18, revenus: 245000 },
        { mois: 'Mar', objets: 67, utilisateurs: 28, reservations: 25, revenus: 320000 },
        { mois: 'Avr', objets: 73, utilisateurs: 35, reservations: 31, revenus: 410000 },
        { mois: 'Mai', objets: 89, utilisateurs: 42, reservations: 38, revenus: 485000 },
        { mois: 'Juin', objets: 94, utilisateurs: 38, reservations: 32, revenus: 425000 }
      ]
    };
    
    logRequest('getStats', {}, stats);
    return stats;
  }
  
  // === CONFIGURATION ===
  
  static getConfig(): MockConfig {
    return { ...MOCK_CONFIG };
  }
  
  static updateConfig(newConfig: Partial<MockConfig>): void {
    Object.assign(MOCK_CONFIG, newConfig);
  }
  
  static clearCache(): void {
    cache.clear();
    console.log('[MockService] Cache cleared');
  }
}

export default MockService;
