import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Package, Star, MessageSquare, Plus, Edit, Trash2 } from 'lucide-react';

const Dashboard = () => {
  const { user, profile, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-1/4 mb-4" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-32 bg-muted rounded" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  const stats = [
    {
      title: "Objets actifs",
      value: "0",
      description: "Objets disponibles à la location",
      icon: Package,
      color: "text-blue-600"
    },
    {
      title: "Réservations",
      value: "0",
      description: "Réservations ce mois",
      icon: Calendar,
      color: "text-green-600"
    },
    {
      title: "Note moyenne",
      value: "0",
      description: "Basé sur les avis clients",
      icon: Star,
      color: "text-yellow-600"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">
            Bienvenue, {profile?.prenom} {profile?.nom}
          </h1>
          <p className="text-muted-foreground">
            Gérez vos objets et réservations depuis votre tableau de bord
          </p>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contenu principal */}
        <Tabs defaultValue={profile?.role === 'loueur' ? 'objets' : 'reservations'} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            {profile?.role === 'loueur' && (
              <TabsTrigger value="objets">Mes objets</TabsTrigger>
            )}
            <TabsTrigger value="reservations">
              {profile?.role === 'loueur' ? 'Réservations reçues' : 'Mes réservations'}
            </TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
            <TabsTrigger value="profil">Mon profil</TabsTrigger>
          </TabsList>

          {profile?.role === 'loueur' && (
            <TabsContent value="objets" className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Mes objets</h2>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un objet
                </Button>
              </div>
              
              <Card>
                <CardContent className="p-6">
                  <div className="text-center py-12">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Aucun objet publié</h3>
                    <p className="text-muted-foreground mb-4">
                      Commencez par ajouter votre premier objet à louer
                    </p>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter un objet
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          <TabsContent value="reservations" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">
                {profile?.role === 'loueur' ? 'Réservations reçues' : 'Mes réservations'}
              </h2>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Aucune réservation</h3>
                  <p className="text-muted-foreground">
                    {profile?.role === 'loueur' 
                      ? 'Vous n\'avez pas encore reçu de réservations'
                      : 'Vous n\'avez pas encore fait de réservations'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="messages" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Messages</h2>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Aucun message</h3>
                  <p className="text-muted-foreground">
                    Vos conversations avec les autres utilisateurs apparaîtront ici
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profil" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Informations personnelles</CardTitle>
                <CardDescription>
                  Gérez vos informations de profil
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Prénom</label>
                    <p className="text-sm text-muted-foreground">{profile?.prenom || 'Non renseigné'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Nom</label>
                    <p className="text-sm text-muted-foreground">{profile?.nom || 'Non renseigné'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Email</label>
                    <p className="text-sm text-muted-foreground">{profile?.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Téléphone</label>
                    <p className="text-sm text-muted-foreground">{profile?.telephone || 'Non renseigné'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Ville</label>
                    <p className="text-sm text-muted-foreground">{profile?.ville}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Commune</label>
                    <p className="text-sm text-muted-foreground">{profile?.commune || 'Non renseigné'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Rôle</label>
                    <Badge variant="outline" className="w-fit">
                      {profile?.role === 'loueur' ? 'Loueur' : 'Locataire'}
                    </Badge>
                  </div>
                </div>
                
                <div className="pt-4">
                  <Button variant="outline">
                    <Edit className="h-4 w-4 mr-2" />
                    Modifier mes informations
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;