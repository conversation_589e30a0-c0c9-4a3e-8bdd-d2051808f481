import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/MockAuthContext';
import { Navigate, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AvatarUpload from '@/components/AvatarUpload';
import ReservationsList from '@/components/ReservationsList';
import { Calendar, Package, Star, MessageSquare, Plus, Edit, User, Settings, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Reservation } from '@/types/profile';

const Dashboard = () => {
  const { user, profile, loading } = useAuth();
  const [activeTab, setActiveTab] = useState('reservations');

  // Mettre à jour l'onglet actif quand le profil est chargé
  useEffect(() => {
    if (profile?.role === 'loueur') {
      setActiveTab('objets');
    } else {
      setActiveTab('reservations');
    }
  }, [profile?.role]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/2 mb-4" />
            <div className="grid grid-cols-3 gap-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-20 bg-muted rounded" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  const stats = [
    {
      title: "Objets",
      value: "0",
      description: "Actifs",
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "Réservations",
      value: "0",
      description: "Ce mois",
      icon: Calendar,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "Note",
      value: "0",
      description: "Moyenne",
      icon: Star,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Header */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur-lg border-b border-border/40 px-4 py-4">
        <div className="flex items-center space-x-3">
          <Avatar className="h-12 w-12">
            <AvatarFallback className="bg-primary text-primary-foreground">
              {profile?.prenom?.[0]}{profile?.nom?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h1 className="text-lg font-bold truncate">
              {profile?.prenom} {profile?.nom}
            </h1>
            <p className="text-sm text-muted-foreground">
              {profile?.role === 'loueur' ? 'Loueur' : 'Locataire'}
            </p>
          </div>
          <Button variant="ghost" size="icon">
            <Settings className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="px-4 py-6">
        {/* Quick Stats - Mobile optimized */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          {stats.map((stat, index) => (
            <Card
              key={index}
              className={cn(
                "text-center transition-all duration-300 hover:shadow-card transform hover:scale-105",
                "animate-scale-in"
              )}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-3">
                <div className={cn("w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2", stat.bgColor)}>
                  <stat.icon className={cn("h-4 w-4", stat.color)} />
                </div>
                <div className="text-lg font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">{stat.title}</p>
                <p className="text-xs text-muted-foreground">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mobile Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 h-12">
            {profile?.role === 'loueur' && (
              <TabsTrigger value="objets" className="text-xs">
                <Package className="h-4 w-4 mr-1" />
                Objets
              </TabsTrigger>
            )}
            <TabsTrigger value="reservations" className="text-xs">
              <Calendar className="h-4 w-4 mr-1" />
              {profile?.role === 'loueur' ? 'Reçues' : 'Réservations'}
            </TabsTrigger>
            <TabsTrigger value="profil" className="text-xs">
              <User className="h-4 w-4 mr-1" />
              Profil
            </TabsTrigger>
          </TabsList>

          {profile?.role === 'loueur' && (
            <TabsContent value="objets" className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Mes objets</h2>
                <Link to="/objet/nouveau">
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-1" />
                    Ajouter
                  </Button>
                </Link>
              </div>

              <Card className="border-dashed border-2">
                <CardContent className="p-6">
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Package className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">Aucun objet publié</h3>
                    <p className="text-muted-foreground text-sm mb-4">
                      Commencez par ajouter votre premier objet à louer
                    </p>
                    <Link to="/objet/nouveau">
                      <Button className="w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        Ajouter mon premier objet
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          <TabsContent value="reservations" className="space-y-4">
            <ReservationsList
              reservations={[]} // TODO: Load actual reservations from API
              onCancelReservation={async (reservationId) => {
                // TODO: Implement cancel reservation
                console.log('Cancel reservation:', reservationId);
              }}
              onContactOwner={(reservation) => {
                // TODO: Implement contact owner
                console.log('Contact owner:', reservation);
              }}
            />
          </TabsContent>

          <TabsContent value="profil" className="space-y-4">
            {/* Profile Header */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <AvatarUpload size="lg" showUploadButton={false} />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold">
                      {profile?.prenom} {profile?.nom}
                    </h3>
                    <p className="text-muted-foreground text-sm">{profile?.email}</p>
                    <Badge variant="outline" className="mt-1">
                      {profile?.role === 'loueur' ? 'Loueur' : 'Locataire'}
                    </Badge>
                  </div>
                </div>

                <Link to="/profile/edit" className="w-full">
                  <Button variant="outline" className="w-full">
                    <Edit className="h-4 w-4 mr-2" />
                    Modifier mon profil
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Profile Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informations personnelles</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between py-2 border-b border-border/50">
                    <span className="text-sm font-medium">Prénom</span>
                    <span className="text-sm text-muted-foreground">{profile?.prenom || 'Non renseigné'}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-border/50">
                    <span className="text-sm font-medium">Nom</span>
                    <span className="text-sm text-muted-foreground">{profile?.nom || 'Non renseigné'}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-border/50">
                    <span className="text-sm font-medium">Téléphone</span>
                    <span className="text-sm text-muted-foreground">{profile?.telephone || 'Non renseigné'}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-border/50">
                    <span className="text-sm font-medium">Ville</span>
                    <span className="text-sm text-muted-foreground">{profile?.ville}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="text-sm font-medium">Commune</span>
                    <span className="text-sm text-muted-foreground">{profile?.commune || 'Non renseigné'}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Actions rapides</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link to="/explorer">
                  <Button variant="outline" className="w-full justify-start">
                    <Package className="h-4 w-4 mr-2" />
                    Explorer les objets
                  </Button>
                </Link>
                {profile?.role === 'loueur' && (
                  <Link to="/objet/nouveau">
                    <Button variant="outline" className="w-full justify-start">
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter un objet
                    </Button>
                  </Link>
                )}
                <Button variant="outline" className="w-full justify-start text-destructive hover:text-destructive">
                  <Edit className="h-4 w-4 mr-2" />
                  Se déconnecter
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;