// Admin Panel Types and Interfaces

export interface AdminUser {
  id: string;
  email: string;
  nom: string;
  prenom: string;
  role: 'admin' | 'super_admin' | 'moderator';
  permissions: AdminPermission[];
  created_at: string;
  last_login: string;
  is_active: boolean;
}

export type AdminPermission = 
  | 'users.view'
  | 'users.edit'
  | 'users.suspend'
  | 'objects.view'
  | 'objects.approve'
  | 'objects.reject'
  | 'reservations.view'
  | 'reservations.manage'
  | 'content.moderate'
  | 'analytics.view'
  | 'system.configure'
  | 'reports.export';

// User Management Types
export interface AdminUserOverview {
  id: string;
  email: string;
  nom: string;
  prenom: string;
  role: 'locataire' | 'loueur';
  verification_status: 'pending' | 'verified' | 'rejected';
  account_status: 'active' | 'suspended' | 'banned';
  created_at: string;
  last_login: string;
  total_reservations: number;
  total_objects: number;
  total_revenue: number;
}

export interface UserStats {
  total_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
  active_users: number;
  suspended_users: number;
  verified_users: number;
  pending_verification: number;
}

// Object Management Types
export interface AdminObjectOverview {
  id: string;
  titre: string;
  description: string;
  prix_par_jour: number;
  categorie: string;
  ville: string;
  commune: string;
  status: 'pending' | 'approved' | 'rejected' | 'reported';
  owner: {
    id: string;
    nom: string;
    prenom: string;
    email: string;
  };
  images: string[];
  created_at: string;
  approved_at?: string;
  rejected_at?: string;
  rejection_reason?: string;
  report_count: number;
  view_count: number;
  reservation_count: number;
}

export interface ObjectStats {
  total_objects: number;
  pending_approval: number;
  approved_objects: number;
  rejected_objects: number;
  reported_objects: number;
  objects_today: number;
  objects_this_week: number;
  objects_this_month: number;
}

// Reservation Management Types
export interface AdminReservationOverview {
  id: string;
  objet: {
    id: string;
    titre: string;
    prix_par_jour: number;
  };
  locataire: {
    id: string;
    nom: string;
    prenom: string;
    email: string;
  };
  loueur: {
    id: string;
    nom: string;
    prenom: string;
    email: string;
  };
  date_debut: string;
  date_fin: string;
  prix_total: number;
  commission: number;
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'disputed';
  payment_status: 'pending' | 'paid' | 'refunded' | 'failed';
  stripe_session_id?: string;
  created_at: string;
  dispute_reason?: string;
  dispute_status?: 'open' | 'investigating' | 'resolved';
}

export interface ReservationStats {
  total_reservations: number;
  pending_reservations: number;
  active_reservations: number;
  completed_reservations: number;
  cancelled_reservations: number;
  disputed_reservations: number;
  total_revenue: number;
  total_commission: number;
  revenue_today: number;
  revenue_this_week: number;
  revenue_this_month: number;
}

// Content Moderation Types
export interface ContentReview {
  id: string;
  type: 'object_description' | 'object_image' | 'user_profile' | 'review';
  content_id: string;
  content_text?: string;
  content_url?: string;
  flagged_reason: string[];
  flagged_by: 'system' | 'user';
  reporter_id?: string;
  status: 'pending' | 'approved' | 'rejected' | 'escalated';
  reviewed_by?: string;
  reviewed_at?: string;
  action_taken?: string;
  created_at: string;
}

export interface ModerationStats {
  pending_reviews: number;
  approved_today: number;
  rejected_today: number;
  escalated_cases: number;
  auto_flagged: number;
  user_reported: number;
}

// Analytics Types
export interface PlatformAnalytics {
  users: {
    total: number;
    growth_rate: number;
    retention_rate: number;
    churn_rate: number;
  };
  objects: {
    total: number;
    approval_rate: number;
    average_price: number;
    most_popular_category: string;
  };
  reservations: {
    total: number;
    conversion_rate: number;
    average_booking_value: number;
    repeat_booking_rate: number;
  };
  revenue: {
    total: number;
    commission_earned: number;
    growth_rate: number;
    average_commission_rate: number;
  };
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
  }[];
}

// System Configuration Types
export interface SystemConfig {
  id: string;
  key: string;
  value: string | number | boolean;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: 'general' | 'payments' | 'notifications' | 'moderation';
  description: string;
  updated_by: string;
  updated_at: string;
}

export interface CommissionConfig {
  id: string;
  category_id?: string;
  min_price: number;
  max_price: number;
  commission_rate: number;
  is_active: boolean;
  created_at: string;
}

// Audit Log Types
export interface AdminLog {
  id: string;
  admin_id: string;
  admin_name: string;
  action: string;
  resource_type: 'user' | 'object' | 'reservation' | 'content' | 'system';
  resource_id: string;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  created_at: string;
}

// Filter and Search Types
export interface AdminFilters {
  search?: string;
  status?: string;
  role?: string;
  date_from?: string;
  date_to?: string;
  category?: string;
  city?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginationInfo {
  current_page: number;
  total_pages: number;
  total_items: number;
  items_per_page: number;
  has_next: boolean;
  has_previous: boolean;
}

// API Response Types
export interface AdminApiResponse<T> {
  success: boolean;
  data: T;
  pagination?: PaginationInfo;
  message?: string;
  errors?: string[];
}

// Export Types
export interface ExportConfig {
  format: 'csv' | 'pdf' | 'excel';
  date_range: {
    start: string;
    end: string;
  };
  filters: AdminFilters;
  columns: string[];
}

// Dashboard Widget Types
export interface DashboardWidget {
  id: string;
  title: string;
  type: 'stat' | 'chart' | 'table' | 'list';
  size: 'small' | 'medium' | 'large';
  data: any;
  refresh_interval?: number;
  last_updated: string;
}

export interface AdminDashboard {
  widgets: DashboardWidget[];
  layout: {
    [key: string]: {
      x: number;
      y: number;
      w: number;
      h: number;
    };
  };
}
