// Test complet du système de données mock

import { MockService } from '@/data/mockData/mockService';

export const testMockDataSystem = async () => {
  console.log('🧪 TEST COMPLET DU SYSTÈME DE DONNÉES MOCK');
  console.log('==========================================');
  console.log('Date:', new Date().toLocaleString('fr-FR'));
  console.log('');

  const results = {
    objects: { total: 0, categories: 0, locations: 0 },
    categories: { total: 0, main: 0, sub: 0 },
    users: { total: 0, loueurs: 0, locataires: 0 },
    authentication: { working: false },
    explorer: { functional: false },
    performance: { loadTime: 0 }
  };

  // Test 1: Données des objets
  console.log('📦 1. TEST DES OBJETS');
  console.log('--------------------');
  
  try {
    const startTime = performance.now();
    const objectsResult = await MockService.getObjects();
    const loadTime = performance.now() - startTime;
    
    results.objects.total = objectsResult.objets.length;
    results.performance.loadTime = loadTime;
    
    console.log(`   ✅ Objets chargés: ${objectsResult.objets.length}`);
    console.log(`   ⏱️ Temps de chargement: ${loadTime.toFixed(2)}ms`);
    
    // Test des catégories d'objets
    const categories = new Set(objectsResult.objets.map(obj => obj.categorie_id));
    results.objects.categories = categories.size;
    console.log(`   📂 Catégories représentées: ${categories.size}`);
    
    // Test des localisations
    const locations = new Set(objectsResult.objets.map(obj => obj.localisation_ville));
    results.objects.locations = locations.size;
    console.log(`   📍 Villes représentées: ${locations.size}`);
    
    // Test des prix
    const prices = objectsResult.objets.map(obj => obj.prix_par_jour);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    console.log(`   💰 Fourchette de prix: ${minPrice} - ${maxPrice} FCFA`);
    
    // Test des objets disponibles
    const available = objectsResult.objets.filter(obj => obj.disponible).length;
    console.log(`   ✅ Objets disponibles: ${available}/${objectsResult.objets.length}`);
    
  } catch (error) {
    console.log('   ❌ Erreur lors du chargement des objets:', error);
  }

  // Test 2: Données des catégories
  console.log('\n📂 2. TEST DES CATÉGORIES');
  console.log('-------------------------');
  
  try {
    const categories = await MockService.getCategories();
    results.categories.total = categories.length;
    
    const mainCategories = categories.filter(cat => !cat.parent_id);
    const subCategories = categories.filter(cat => cat.parent_id);
    
    results.categories.main = mainCategories.length;
    results.categories.sub = subCategories.length;
    
    console.log(`   📂 Total catégories: ${categories.length}`);
    console.log(`   📁 Catégories principales: ${mainCategories.length}`);
    console.log(`   📄 Sous-catégories: ${subCategories.length}`);
    
    // Test des icônes
    const withIcons = categories.filter(cat => cat.icone).length;
    console.log(`   🎨 Avec icônes: ${withIcons}/${categories.length}`);
    
    // Test des couleurs
    const withColors = categories.filter(cat => cat.color).length;
    console.log(`   🌈 Avec couleurs: ${withColors}/${categories.length}`);
    
  } catch (error) {
    console.log('   ❌ Erreur lors du chargement des catégories:', error);
  }

  // Test 3: Données des utilisateurs
  console.log('\n👥 3. TEST DES UTILISATEURS');
  console.log('---------------------------');
  
  try {
    const users = await MockService.getUsers();
    results.users.total = users.length;
    
    const loueurs = users.filter(user => user.role === 'loueur');
    const locataires = users.filter(user => user.role === 'locataire');
    const admins = users.filter(user => user.role === 'admin');
    
    results.users.loueurs = loueurs.length;
    results.users.locataires = locataires.length;
    
    console.log(`   👥 Total utilisateurs: ${users.length}`);
    console.log(`   🏠 Loueurs: ${loueurs.length}`);
    console.log(`   🔍 Locataires: ${locataires.length}`);
    console.log(`   👑 Admins: ${admins.length}`);
    
    // Test des villes
    const userCities = new Set(users.map(user => user.ville));
    console.log(`   🌍 Villes utilisateurs: ${userCities.size}`);
    
    // Test des notes
    const avgRating = users.reduce((sum, user) => sum + user.note_moyenne, 0) / users.length;
    console.log(`   ⭐ Note moyenne: ${avgRating.toFixed(1)}/5.0`);
    
  } catch (error) {
    console.log('   ❌ Erreur lors du chargement des utilisateurs:', error);
  }

  // Test 4: Authentification
  console.log('\n🔐 4. TEST DE L\'AUTHENTIFICATION');
  console.log('--------------------------------');
  
  try {
    // Test de connexion avec utilisateur test
    const loginResult = await MockService.signIn('<EMAIL>', 'password123');
    
    if (loginResult.user && !loginResult.error) {
      console.log('   ✅ Connexion réussie');
      console.log(`   👤 Utilisateur: ${loginResult.user.prenom} ${loginResult.user.nom}`);
      console.log(`   📧 Email: ${loginResult.user.email}`);
      console.log(`   🏷️ Rôle: ${loginResult.user.role}`);
      results.authentication.working = true;
    } else {
      console.log('   ❌ Échec de la connexion:', loginResult.error);
    }
    
    // Test de connexion avec mauvais identifiants
    const badLoginResult = await MockService.signIn('<EMAIL>', 'wrongpassword');
    if (badLoginResult.error) {
      console.log('   ✅ Rejet des mauvais identifiants fonctionne');
    }
    
  } catch (error) {
    console.log('   ❌ Erreur lors du test d\'authentification:', error);
  }

  // Test 5: Filtrage et recherche
  console.log('\n🔍 5. TEST DU FILTRAGE ET RECHERCHE');
  console.log('-----------------------------------');
  
  try {
    // Test de recherche par mot-clé
    const searchResult = await MockService.getObjects({ search: 'perceuse' });
    console.log(`   🔍 Recherche "perceuse": ${searchResult.objets.length} résultats`);
    
    // Test de filtrage par ville
    const abidjanResult = await MockService.getObjects({ ville: 'Abidjan' });
    console.log(`   📍 Objets à Abidjan: ${abidjanResult.objets.length}`);
    
    // Test de filtrage par catégorie
    const outilsResult = await MockService.getObjects({ categorie: 'outils-electriques' });
    console.log(`   🔧 Outils électriques: ${outilsResult.objets.length}`);
    
    // Test de filtrage par prix
    const cheapResult = await MockService.getObjects({ prix_max: 10000 });
    console.log(`   💰 Objets < 10 000 FCFA: ${cheapResult.objets.length}`);
    
    results.explorer.functional = true;
    
  } catch (error) {
    console.log('   ❌ Erreur lors du test de filtrage:', error);
  }

  // Test 6: Performance et cache
  console.log('\n⚡ 6. TEST DE PERFORMANCE');
  console.log('------------------------');
  
  try {
    // Test de performance avec cache
    const start1 = performance.now();
    await MockService.getObjects();
    const time1 = performance.now() - start1;
    
    const start2 = performance.now();
    await MockService.getObjects(); // Devrait utiliser le cache
    const time2 = performance.now() - start2;
    
    console.log(`   ⏱️ Premier appel: ${time1.toFixed(2)}ms`);
    console.log(`   ⚡ Deuxième appel (cache): ${time2.toFixed(2)}ms`);
    console.log(`   📈 Amélioration: ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
    
    // Test de la configuration
    const config = MockService.getConfig();
    console.log(`   ⚙️ Délai simulé: ${config.delay_simulation}ms`);
    console.log(`   📊 Taux d'erreur: ${(config.error_rate * 100).toFixed(1)}%`);
    console.log(`   💾 Cache activé: ${config.cache_enabled ? 'Oui' : 'Non'}`);
    
  } catch (error) {
    console.log('   ❌ Erreur lors du test de performance:', error);
  }

  // Rapport final
  console.log('\n🎯 RAPPORT FINAL');
  console.log('================');
  
  const totalScore = (
    (results.objects.total > 0 ? 1 : 0) +
    (results.categories.total > 0 ? 1 : 0) +
    (results.users.total > 0 ? 1 : 0) +
    (results.authentication.working ? 1 : 0) +
    (results.explorer.functional ? 1 : 0) +
    (results.performance.loadTime < 1000 ? 1 : 0)
  );
  
  const maxScore = 6;
  const percentage = (totalScore / maxScore * 100).toFixed(0);
  
  console.log(`📊 Score global: ${totalScore}/${maxScore} (${percentage}%)`);
  console.log('');
  
  console.log('📈 Statistiques détaillées:');
  console.log(`   📦 Objets: ${results.objects.total} (${results.objects.categories} catégories, ${results.objects.locations} villes)`);
  console.log(`   📂 Catégories: ${results.categories.total} (${results.categories.main} principales, ${results.categories.sub} sous-catégories)`);
  console.log(`   👥 Utilisateurs: ${results.users.total} (${results.users.loueurs} loueurs, ${results.users.locataires} locataires)`);
  console.log(`   🔐 Authentification: ${results.authentication.working ? 'Fonctionnelle' : 'Défaillante'}`);
  console.log(`   🔍 Explorer: ${results.explorer.functional ? 'Fonctionnel' : 'Défaillant'}`);
  console.log(`   ⚡ Performance: ${results.performance.loadTime.toFixed(2)}ms`);
  console.log('');
  
  if (totalScore === maxScore) {
    console.log('🎉 SYSTÈME MOCK PARFAITEMENT FONCTIONNEL !');
    console.log('✅ Toutes les fonctionnalités sont opérationnelles');
    console.log('✅ L\'application peut fonctionner sans Supabase');
    console.log('✅ Prêt pour les tests utilisateur');
  } else if (totalScore >= 4) {
    console.log('✅ SYSTÈME MOCK LARGEMENT FONCTIONNEL');
    console.log('⚠️ Quelques améliorations possibles');
  } else {
    console.log('⚠️ SYSTÈME MOCK PARTIELLEMENT FONCTIONNEL');
    console.log('🔧 Corrections nécessaires');
  }
  
  console.log('');
  console.log('📋 PROCHAINES ÉTAPES:');
  console.log('1. Tester l\'interface utilisateur');
  console.log('2. Valider les fonctionnalités de filtrage');
  console.log('3. Tester l\'authentification complète');
  console.log('4. Vérifier la navigation entre les pages');
  
  return results;
};

// Export pour utilisation
export default testMockDataSystem;

// Instructions d'utilisation
console.log(`
🧪 TEST DU SYSTÈME DE DONNÉES MOCK

Pour tester le système complet :
1. Ouvrez la console du navigateur (F12)
2. Exécutez : testMockDataSystem()
3. Analysez les résultats détaillés
4. Testez manuellement l'interface
`);
