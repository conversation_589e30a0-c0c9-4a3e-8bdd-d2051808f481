import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Calendar, Clock, CreditCard, MessageSquare, User, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  ReservationFormData, 
  ReservationValidationErrors, 
  FormState 
} from '@/types/profile';
import { 
  validateReservationForm, 
  hasValidationErrors, 
  calculateTotalPrice,
  formatCurrency,
  formatDate
} from '@/utils/profileValidation';

interface ObjetData {
  id: string;
  titre: string;
  prix_par_jour: number;
  images: string[];
  description: string;
  ville: string;
  commune?: string;
  owner: {
    nom: string;
    prenom: string;
    avatar_url?: string;
  };
}

interface ReservationBookingProps {
  objet: ObjetData;
  onReservationSubmit: (data: ReservationFormData) => Promise<void>;
  className?: string;
}

const ReservationBooking: React.FC<ReservationBookingProps> = ({
  objet,
  onReservationSubmit,
  className
}) => {
  const { user, profile } = useAuth();
  const { toast } = useToast();

  const [formData, setFormData] = useState<ReservationFormData>({
    start_date: '',
    end_date: '',
    message: ''
  });

  const [errors, setErrors] = useState<ReservationValidationErrors>({});
  const [formState, setFormState] = useState<FormState>('idle');

  const handleInputChange = (field: keyof ReservationFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const validationErrors = validateReservationForm(formData);
    setErrors(validationErrors);
    return !hasValidationErrors(validationErrors);
  };

  const calculateDays = (): number => {
    if (!formData.start_date || !formData.end_date) return 0;
    const start = new Date(formData.start_date);
    const end = new Date(formData.end_date);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const getTotalPrice = (): number => {
    if (!formData.start_date || !formData.end_date) return 0;
    return calculateTotalPrice(objet.prix_par_jour, formData.start_date, formData.end_date);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "Connexion requise",
        description: "Vous devez être connecté pour effectuer une réservation",
        variant: "destructive"
      });
      return;
    }

    if (!validateForm()) {
      toast({
        title: "Erreur de validation",
        description: "Veuillez corriger les erreurs avant de continuer",
        variant: "destructive"
      });
      return;
    }

    setFormState('loading');

    try {
      await onReservationSubmit(formData);
      
      setFormState('success');
      toast({
        title: "Réservation envoyée",
        description: "Votre demande de réservation a été envoyée au propriétaire"
      });

      // Reset form
      setFormData({
        start_date: '',
        end_date: '',
        message: ''
      });

    } catch (error) {
      setFormState('error');
      toast({
        title: "Erreur de réservation",
        description: "Une erreur s'est produite lors de l'envoi de votre réservation",
        variant: "destructive"
      });
    }
  };

  const days = calculateDays();
  const totalPrice = getTotalPrice();

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Réserver cet objet</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Object Info Summary */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-2">
            <h3 className="font-semibold text-lg">{objet.titre}</h3>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span>{objet.ville}{objet.commune && `, ${objet.commune}`}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-primary">
                {formatCurrency(objet.prix_par_jour)}
              </span>
              <span className="text-sm text-muted-foreground">par jour</span>
            </div>
          </div>

          {/* Owner Info */}
          <div className="flex items-center space-x-3 p-3 bg-accent/50 rounded-lg">
            <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold">
              {objet.owner.prenom[0]}{objet.owner.nom[0]}
            </div>
            <div>
              <p className="font-medium">{objet.owner.prenom} {objet.owner.nom}</p>
              <p className="text-sm text-muted-foreground">Propriétaire</p>
            </div>
          </div>

          {/* Date Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date" className="text-sm font-medium">
                Date de début <span className="text-destructive">*</span>
              </Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                className={cn(errors.start_date && "border-destructive")}
                min={new Date().toISOString().split('T')[0]}
              />
              {errors.start_date && (
                <p className="text-sm text-destructive animate-slide-up">{errors.start_date}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date" className="text-sm font-medium">
                Date de fin <span className="text-destructive">*</span>
              </Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                className={cn(errors.end_date && "border-destructive")}
                min={formData.start_date || new Date().toISOString().split('T')[0]}
              />
              {errors.end_date && (
                <p className="text-sm text-destructive animate-slide-up">{errors.end_date}</p>
              )}
            </div>
          </div>

          {/* Price Calculation */}
          {days > 0 && (
            <div className="bg-primary/5 border border-primary/20 rounded-lg p-4 space-y-2 animate-slide-up">
              <div className="flex justify-between items-center">
                <span className="text-sm">Durée de location</span>
                <span className="font-medium">{days} jour{days > 1 ? 's' : ''}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Prix par jour</span>
                <span className="font-medium">{formatCurrency(objet.prix_par_jour)}</span>
              </div>
              <div className="border-t border-primary/20 pt-2 flex justify-between items-center">
                <span className="font-semibold">Total</span>
                <span className="text-xl font-bold text-primary">{formatCurrency(totalPrice)}</span>
              </div>
            </div>
          )}

          {/* Message */}
          <div className="space-y-2">
            <Label htmlFor="message" className="text-sm font-medium">
              Message au propriétaire (optionnel)
            </Label>
            <div className="relative">
              <MessageSquare className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                className={cn("pl-10 min-h-[100px]", errors.message && "border-destructive")}
                placeholder="Décrivez vos besoins spécifiques ou posez des questions..."
                maxLength={500}
              />
            </div>
            {errors.message && (
              <p className="text-sm text-destructive animate-slide-up">{errors.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {formData.message.length}/500 caractères
            </p>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full h-12 text-base font-semibold"
            disabled={formState === 'loading' || !user}
          >
            <CreditCard className="h-4 w-4 mr-2" />
            {formState === 'loading' ? 'Envoi en cours...' : 'Envoyer la demande de réservation'}
          </Button>

          {!user && (
            <p className="text-sm text-muted-foreground text-center">
              <a href="/auth" className="text-primary hover:underline">
                Connectez-vous
              </a> pour effectuer une réservation
            </p>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default ReservationBooking;
