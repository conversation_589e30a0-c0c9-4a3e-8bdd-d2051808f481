// Vérification finale complète de l'application AfroRent Hub

export const runFinalVerification = () => {
  console.log('🎯 VÉRIFICATION FINALE AFRORENT HUB');
  console.log('===================================');
  console.log('Date:', new Date().toLocaleString('fr-FR'));
  console.log('');

  const results = {
    pages: {},
    functionality: {},
    errors: [],
    warnings: [],
    performance: {},
    accessibility: {},
    overall: 'PENDING'
  };

  // Test 1: Vérification de toutes les pages
  console.log('📄 1. VÉRIFICATION DES PAGES');
  console.log('----------------------------');
  
  const testPages = () => {
    const pages = [
      { name: 'Accueil', path: '/', expected: ['hero', 'categories', 'statistics'] },
      { name: 'Explorer', path: '/explorer', expected: ['filters', 'search', 'objects-list'] },
      { name: 'Auth', path: '/auth', expected: ['login-form', 'signup-form'] },
      { name: 'Dashboard', path: '/dashboard', expected: ['protected-route'] },
      { name: 'Admin', path: '/admin', expected: ['admin-layout'] }
    ];

    pages.forEach(page => {
      const currentPath = window.location.pathname;
      const isCurrentPage = currentPath === page.path;
      
      if (isCurrentPage) {
        console.log(`   ✅ ${page.name} (${page.path}) - Page actuelle`);
        
        // Vérifier les éléments attendus
        page.expected.forEach(element => {
          const found = document.querySelector(`[class*="${element}"]`) || 
                       document.querySelector(`[data-testid="${element}"]`) ||
                       document.querySelector(`#${element}`);
          console.log(`     ${found ? '✅' : '⚠️'} Élément "${element}"`);
        });
        
        results.pages[page.name] = {
          accessible: true,
          elements: page.expected.map(el => ({
            name: el,
            found: !!document.querySelector(`[class*="${el}"]`)
          }))
        };
      } else {
        console.log(`   ℹ️ ${page.name} (${page.path}) - Non testée (pas la page actuelle)`);
      }
    });
  };

  testPages();

  // Test 2: Vérification des fonctionnalités interactives
  console.log('\n🔧 2. VÉRIFICATION DES FONCTIONNALITÉS');
  console.log('-------------------------------------');
  
  const testFunctionality = () => {
    // Test des boutons
    const buttons = document.querySelectorAll('button');
    console.log(`   📱 Boutons trouvés: ${buttons.length}`);
    
    let workingButtons = 0;
    buttons.forEach(button => {
      if (button.onclick || button.addEventListener || button.type === 'submit') {
        workingButtons++;
      }
    });
    console.log(`   ✅ Boutons fonctionnels: ${workingButtons}/${buttons.length}`);

    // Test des formulaires
    const forms = document.querySelectorAll('form');
    console.log(`   📝 Formulaires trouvés: ${forms.length}`);

    // Test des liens de navigation
    const navLinks = document.querySelectorAll('a[href]');
    console.log(`   🔗 Liens de navigation: ${navLinks.length}`);

    // Test des inputs
    const inputs = document.querySelectorAll('input, select, textarea');
    console.log(`   📋 Champs de saisie: ${inputs.length}`);

    results.functionality = {
      buttons: { total: buttons.length, working: workingButtons },
      forms: forms.length,
      navLinks: navLinks.length,
      inputs: inputs.length
    };
  };

  testFunctionality();

  // Test 3: Vérification des erreurs console
  console.log('\n🐛 3. VÉRIFICATION DES ERREURS CONSOLE');
  console.log('-------------------------------------');
  
  const testConsoleErrors = () => {
    // Capturer les erreurs pendant 3 secondes
    const originalError = console.error;
    const originalWarn = console.warn;
    
    const errors = [];
    const warnings = [];

    console.error = (...args) => {
      errors.push(args.join(' '));
      originalError.apply(console, args);
    };

    console.warn = (...args) => {
      warnings.push(args.join(' '));
      originalWarn.apply(console, args);
    };

    setTimeout(() => {
      console.error = originalError;
      console.warn = originalWarn;
      
      console.log(`   🚨 Erreurs détectées: ${errors.length}`);
      console.log(`   ⚠️ Avertissements: ${warnings.length}`);
      
      if (errors.length > 0) {
        console.log('   Détails des erreurs:');
        errors.forEach((error, i) => {
          console.log(`     ${i + 1}. ${error}`);
        });
      }
      
      if (warnings.length > 0) {
        console.log('   Détails des avertissements:');
        warnings.forEach((warning, i) => {
          console.log(`     ${i + 1}. ${warning}`);
        });
      }

      results.errors = errors;
      results.warnings = warnings;
    }, 3000);
  };

  testConsoleErrors();

  // Test 4: Vérification de la responsivité
  console.log('\n📱 4. VÉRIFICATION DE LA RESPONSIVITÉ');
  console.log('------------------------------------');
  
  const testResponsiveness = () => {
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      isMobile: window.innerWidth < 768,
      isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
      isDesktop: window.innerWidth >= 1024
    };

    console.log(`   📐 Viewport: ${viewport.width}x${viewport.height}`);
    console.log(`   📱 Mobile: ${viewport.isMobile ? 'Oui' : 'Non'}`);
    console.log(`   📟 Tablet: ${viewport.isTablet ? 'Oui' : 'Non'}`);
    console.log(`   🖥️ Desktop: ${viewport.isDesktop ? 'Oui' : 'Non'}`);

    // Test des éléments responsive
    const mobileElements = document.querySelectorAll('[class*="mobile"]');
    const hiddenOnMobile = document.querySelectorAll('[class*="hidden"][class*="md:"]');
    
    console.log(`   📱 Éléments mobile: ${mobileElements.length}`);
    console.log(`   👁️ Éléments cachés sur mobile: ${hiddenOnMobile.length}`);

    results.performance.viewport = viewport;
  };

  testResponsiveness();

  // Test 5: Vérification de l'accessibilité
  console.log('\n♿ 5. VÉRIFICATION DE L\'ACCESSIBILITÉ');
  console.log('------------------------------------');
  
  const testAccessibility = () => {
    const accessibilityIssues = [];

    // Test des images sans alt
    const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
    if (imagesWithoutAlt.length > 0) {
      accessibilityIssues.push(`${imagesWithoutAlt.length} image(s) sans attribut alt`);
    }

    // Test des boutons sans texte accessible
    const inaccessibleButtons = document.querySelectorAll('button:not([aria-label]):empty');
    if (inaccessibleButtons.length > 0) {
      accessibilityIssues.push(`${inaccessibleButtons.length} bouton(s) sans texte accessible`);
    }

    // Test des liens sans texte
    const emptyLinks = document.querySelectorAll('a:empty:not([aria-label])');
    if (emptyLinks.length > 0) {
      accessibilityIssues.push(`${emptyLinks.length} lien(s) vide(s)`);
    }

    // Test des inputs sans label
    const unlabeledInputs = document.querySelectorAll('input:not([aria-label]):not([id])');
    if (unlabeledInputs.length > 0) {
      accessibilityIssues.push(`${unlabeledInputs.length} input(s) sans label`);
    }

    console.log(`   ♿ Problèmes d'accessibilité: ${accessibilityIssues.length}`);
    
    if (accessibilityIssues.length === 0) {
      console.log('   ✅ Aucun problème d\'accessibilité majeur');
    } else {
      accessibilityIssues.forEach(issue => {
        console.log(`   ⚠️ ${issue}`);
      });
    }

    results.accessibility = {
      issues: accessibilityIssues,
      score: accessibilityIssues.length === 0 ? 'EXCELLENT' : 
             accessibilityIssues.length <= 2 ? 'BON' : 'À AMÉLIORER'
    };
  };

  testAccessibility();

  // Test 6: Vérification des performances
  console.log('\n⚡ 6. VÉRIFICATION DES PERFORMANCES');
  console.log('----------------------------------');
  
  const testPerformance = () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (navigation) {
      const metrics = {
        loadTime: navigation.loadEventEnd - navigation.fetchStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        firstPaint: 0,
        firstContentfulPaint: 0
      };

      // Get paint metrics if available
      const paintEntries = performance.getEntriesByType('paint');
      paintEntries.forEach(entry => {
        if (entry.name === 'first-paint') {
          metrics.firstPaint = entry.startTime;
        } else if (entry.name === 'first-contentful-paint') {
          metrics.firstContentfulPaint = entry.startTime;
        }
      });

      console.log(`   ⏱️ Temps de chargement: ${metrics.loadTime.toFixed(2)}ms`);
      console.log(`   📄 DOM Content Loaded: ${metrics.domContentLoaded.toFixed(2)}ms`);
      
      if (metrics.firstPaint > 0) {
        console.log(`   🎨 First Paint: ${metrics.firstPaint.toFixed(2)}ms`);
      }
      
      if (metrics.firstContentfulPaint > 0) {
        console.log(`   🖼️ First Contentful Paint: ${metrics.firstContentfulPaint.toFixed(2)}ms`);
      }

      // Performance score
      let performanceScore = 'EXCELLENT';
      if (metrics.loadTime > 3000) performanceScore = 'À AMÉLIORER';
      else if (metrics.loadTime > 1500) performanceScore = 'BON';

      console.log(`   📊 Score de performance: ${performanceScore}`);

      results.performance = {
        ...results.performance,
        metrics,
        score: performanceScore
      };
    }

    // Memory usage if available
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
      console.log(`   💾 Mémoire utilisée: ${usedMB}MB`);
      results.performance.memoryUsage = `${usedMB}MB`;
    }
  };

  testPerformance();

  // Rapport final après 5 secondes
  setTimeout(() => {
    console.log('\n🎯 RAPPORT FINAL');
    console.log('================');
    
    const errorCount = results.errors.length;
    const warningCount = results.warnings.length;
    const accessibilityScore = results.accessibility.score;
    const performanceScore = results.performance.score;

    // Calcul du score global
    let overallScore = 'EXCELLENT';
    if (errorCount > 0) overallScore = 'CRITIQUE';
    else if (warningCount > 3 || accessibilityScore === 'À AMÉLIORER') overallScore = 'À AMÉLIORER';
    else if (warningCount > 0 || performanceScore === 'BON') overallScore = 'BON';

    results.overall = overallScore;

    console.log(`🏆 Score global: ${overallScore}`);
    console.log(`🐛 Erreurs: ${errorCount}`);
    console.log(`⚠️ Avertissements: ${warningCount}`);
    console.log(`♿ Accessibilité: ${accessibilityScore}`);
    console.log(`⚡ Performance: ${performanceScore}`);
    console.log('');

    if (overallScore === 'EXCELLENT') {
      console.log('🎉 FÉLICITATIONS !');
      console.log('L\'application est prête pour la production.');
      console.log('Aucune erreur critique détectée.');
    } else if (overallScore === 'BON') {
      console.log('✅ APPLICATION FONCTIONNELLE');
      console.log('Quelques améliorations mineures possibles.');
    } else if (overallScore === 'À AMÉLIORER') {
      console.log('⚠️ AMÉLIORATIONS NÉCESSAIRES');
      console.log('Consultez les détails ci-dessus.');
    } else {
      console.log('🚨 PROBLÈMES CRITIQUES DÉTECTÉS');
      console.log('Correction immédiate requise.');
    }

    console.log('\n📋 PROCHAINES ÉTAPES RECOMMANDÉES:');
    if (errorCount === 0 && warningCount === 0) {
      console.log('1. ✅ Tests utilisateur finaux');
      console.log('2. ✅ Audit de sécurité');
      console.log('3. ✅ Déploiement en production');
    } else {
      console.log('1. 🔧 Corriger les erreurs identifiées');
      console.log('2. 🧪 Relancer les tests');
      console.log('3. 📊 Optimiser les performances');
    }

    return results;
  }, 5000);

  return results;
};

// Export pour utilisation
export default runFinalVerification;

// Instructions d'utilisation
console.log(`
🎯 VÉRIFICATION FINALE AFRORENT HUB

Pour exécuter la vérification finale :
1. Naviguez vers la page à tester
2. Ouvrez la console du navigateur (F12)
3. Exécutez : runFinalVerification()
4. Attendez 5 secondes pour le rapport complet
5. Répétez pour chaque page importante
`);
