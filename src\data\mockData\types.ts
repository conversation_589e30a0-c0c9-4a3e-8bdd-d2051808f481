// Types pour le système de données mock

export interface MockUser {
  id: string;
  user_id: string;
  email: string;
  nom: string;
  prenom: string;
  telephone?: string;
  ville: string;
  commune?: string;
  role: 'locataire' | 'loueur' | 'admin' | 'super_admin' | 'moderator';
  avatar_url?: string;
  note_moyenne: number;
  nombre_avis: number;
  date_inscription: string;
  derniere_connexion: string;
  verified: boolean;
  bio?: string;
}

export interface MockObjet {
  id: string;
  titre: string;
  description: string;
  prix_par_jour: number;
  caution: number;
  proprietaire_id: string;
  categorie_id: string;
  localisation_ville: string;
  localisation_commune?: string;
  images: string[];
  disponible: boolean;
  conditions?: string;
  created_at: string;
  updated_at: string;
  vues: number;
  favoris: number;
  note_moyenne: number;
  nombre_avis: number;
  tags: string[];
  etat: 'excellent' | 'tres_bon' | 'bon' | 'correct';
  livraison_possible: boolean;
  prix_livraison?: number;
  duree_min_location: number; // en jours
  duree_max_location: number; // en jours
}

export interface MockCategorie {
  id: string;
  nom: string;
  description: string;
  icone: string;
  color: string;
  parent_id?: string;
  ordre: number;
  nombre_objets: number;
  image_url?: string;
}

export interface MockReservation {
  id: string;
  objet_id: string;
  locataire_id: string;
  proprietaire_id: string;
  date_debut: string;
  date_fin: string;
  prix_total: number;
  caution: number;
  statut: 'en_attente' | 'confirmee' | 'en_cours' | 'terminee' | 'annulee';
  message_locataire?: string;
  message_proprietaire?: string;
  created_at: string;
  updated_at: string;
  lieu_remise?: string;
  heure_remise?: string;
  lieu_retour?: string;
  heure_retour?: string;
  frais_livraison?: number;
}

export interface MockAvis {
  id: string;
  reservation_id: string;
  auteur_id: string;
  cible_id: string; // ID de l'objet ou du user
  type: 'objet' | 'utilisateur';
  note: number;
  commentaire: string;
  created_at: string;
  photos?: string[];
  reponse_proprietaire?: string;
  date_reponse?: string;
}

export interface MockNotification {
  id: string;
  user_id: string;
  type: 'reservation' | 'message' | 'avis' | 'rappel' | 'promotion';
  titre: string;
  message: string;
  lue: boolean;
  created_at: string;
  action_url?: string;
  data?: any;
}

export interface MockMessage {
  id: string;
  conversation_id: string;
  expediteur_id: string;
  destinataire_id: string;
  contenu: string;
  created_at: string;
  lu: boolean;
  type: 'text' | 'image' | 'document';
  fichier_url?: string;
}

export interface MockConversation {
  id: string;
  participants: string[];
  objet_id?: string;
  derniere_activite: string;
  created_at: string;
  archivee: boolean;
}

// Types pour les filtres et recherche
export interface MockFilters {
  search?: string;
  categorie?: string;
  ville?: string;
  commune?: string;
  prix_min?: number;
  prix_max?: number;
  date_debut?: string;
  date_fin?: string;
  disponible_seulement?: boolean;
  livraison_possible?: boolean;
  note_min?: number;
  etat?: string[];
  tags?: string[];
}

export interface MockSearchResult {
  objets: MockObjet[];
  total: number;
  page: number;
  pages_total: number;
  filtres_appliques: MockFilters;
}

// Configuration du système mock
export interface MockConfig {
  enabled: boolean;
  delay_simulation: number; // ms
  error_rate: number; // 0-1
  cache_enabled: boolean;
  log_requests: boolean;
}

// Statistiques pour le dashboard
export interface MockStats {
  total_objets: number;
  total_utilisateurs: number;
  total_reservations: number;
  revenus_total: number;
  objets_populaires: MockObjet[];
  utilisateurs_actifs: MockUser[];
  reservations_recentes: MockReservation[];
  croissance_mensuelle: {
    mois: string;
    objets: number;
    utilisateurs: number;
    reservations: number;
    revenus: number;
  }[];
}

export default {
  MockUser,
  MockObjet,
  MockCategorie,
  MockReservation,
  MockAvis,
  MockNotification,
  MockMessage,
  MockConversation,
  MockFilters,
  MockSearchResult,
  MockConfig,
  MockStats
};
