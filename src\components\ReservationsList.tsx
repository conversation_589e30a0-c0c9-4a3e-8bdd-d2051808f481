import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Calendar, 
  MapPin, 
  MessageSquare, 
  Phone, 
  Star, 
  X, 
  Eye,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  Reservation, 
  RESERVATION_STATUS_LABELS, 
  RESERVATION_STATUS_COLORS 
} from '@/types/profile';
import { 
  formatCurrency, 
  formatDate, 
  formatDateShort 
} from '@/utils/profileValidation';

interface ReservationsListProps {
  reservations: Reservation[];
  onCancelReservation?: (reservationId: string) => Promise<void>;
  onContactOwner?: (reservation: Reservation) => void;
  className?: string;
}

const ReservationsList: React.FC<ReservationsListProps> = ({
  reservations,
  onCancelReservation,
  onContactOwner,
  className
}) => {
  const { toast } = useToast();
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const filteredReservations = reservations.filter(reservation => {
    if (statusFilter === 'all') return true;
    return reservation.status === statusFilter;
  });

  const handleCancelReservation = async (reservationId: string) => {
    if (!onCancelReservation) return;

    const confirmed = window.confirm(
      "Êtes-vous sûr de vouloir annuler cette réservation ?"
    );
    
    if (!confirmed) return;

    setIsLoading(reservationId);

    try {
      await onCancelReservation(reservationId);
      toast({
        title: "Réservation annulée",
        description: "Votre réservation a été annulée avec succès"
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur s'est produite lors de l'annulation",
        variant: "destructive"
      });
    } finally {
      setIsLoading(null);
    }
  };

  const getStatusBadge = (status: Reservation['status']) => {
    return (
      <Badge 
        variant="secondary" 
        className={cn("text-xs", RESERVATION_STATUS_COLORS[status])}
      >
        {RESERVATION_STATUS_LABELS[status]}
      </Badge>
    );
  };

  const canCancelReservation = (reservation: Reservation): boolean => {
    return reservation.status === 'pending' || reservation.status === 'confirmed';
  };

  const getReservationActions = (reservation: Reservation) => {
    const actions = [];

    // View details
    actions.push(
      <Button
        key="view"
        variant="outline"
        size="sm"
        asChild
      >
        <Link to={`/objet/${reservation.objet_id}`}>
          <Eye className="h-3 w-3 mr-1" />
          Voir l'objet
        </Link>
      </Button>
    );

    // Contact owner
    if (onContactOwner && reservation.status !== 'cancelled') {
      actions.push(
        <Button
          key="contact"
          variant="outline"
          size="sm"
          onClick={() => onContactOwner(reservation)}
        >
          <MessageSquare className="h-3 w-3 mr-1" />
          Contacter
        </Button>
      );
    }

    // Cancel reservation
    if (canCancelReservation(reservation) && onCancelReservation) {
      actions.push(
        <Button
          key="cancel"
          variant="destructive"
          size="sm"
          onClick={() => handleCancelReservation(reservation.id)}
          disabled={isLoading === reservation.id}
        >
          <X className="h-3 w-3 mr-1" />
          {isLoading === reservation.id ? 'Annulation...' : 'Annuler'}
        </Button>
      );
    }

    return actions;
  };

  if (reservations.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Aucune réservation</h3>
          <p className="text-muted-foreground mb-4">
            Vous n'avez pas encore effectué de réservation.
          </p>
          <Button asChild>
            <Link to="/explorer">
              Explorer les objets
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Filter */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Mes Réservations</CardTitle>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes</SelectItem>
                  <SelectItem value="pending">En attente</SelectItem>
                  <SelectItem value="confirmed">Confirmées</SelectItem>
                  <SelectItem value="active">En cours</SelectItem>
                  <SelectItem value="completed">Terminées</SelectItem>
                  <SelectItem value="cancelled">Annulées</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Reservations List */}
      {filteredReservations.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              Aucune réservation trouvée pour ce filtre.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {filteredReservations.map((reservation) => (
            <Card key={reservation.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex flex-col space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">
                        {reservation.objet?.titre || 'Objet supprimé'}
                      </h3>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-1">
                        <MapPin className="h-3 w-3" />
                        <span>Propriétaire: {reservation.objet?.owner.prenom} {reservation.objet?.owner.nom}</span>
                      </div>
                    </div>
                    {getStatusBadge(reservation.status)}
                  </div>

                  {/* Dates and Price */}
                  <div className="grid grid-cols-2 gap-4 py-3 bg-muted/30 rounded-lg px-3">
                    <div>
                      <p className="text-xs text-muted-foreground">Période</p>
                      <p className="text-sm font-medium">
                        {formatDateShort(reservation.start_date)} - {formatDateShort(reservation.end_date)}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Total</p>
                      <p className="text-sm font-bold text-primary">
                        {formatCurrency(reservation.total_price)}
                      </p>
                    </div>
                  </div>

                  {/* Message */}
                  {reservation.message && (
                    <div className="bg-accent/50 rounded-lg p-3">
                      <p className="text-xs text-muted-foreground mb-1">Votre message:</p>
                      <p className="text-sm">{reservation.message}</p>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex flex-wrap gap-2 pt-2 border-t border-border/50">
                    {getReservationActions(reservation)}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReservationsList;
