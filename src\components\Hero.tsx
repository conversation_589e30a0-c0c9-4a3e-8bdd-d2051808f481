import { But<PERSON> } from "@/components/ui/button";
import { Search, MapPin, Calendar } from "lucide-react";
import heroImage from "@/assets/hero-image.jpg";

const Hero = () => {
  return (
    <section className="relative min-h-[600px] flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src={heroImage}
          alt="Objets en location"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-black/30"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 text-center text-white">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
            Louez tout ce dont vous avez besoin
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-white/90 animate-slide-up">
            Découvrez des milliers d'objets disponibles à la location près de chez vous.
            Économique, pratique et écologique.
          </p>

          {/* Search Form */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl animate-scale-in">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <input
                  type="text"
                  placeholder="Que cherchez-vous ?"
                  className="w-full pl-10 pr-4 py-3 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring text-foreground bg-background"
                />
              </div>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <input
                  type="text"
                  placeholder="Où ?"
                  className="w-full pl-10 pr-4 py-3 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring text-foreground bg-background"
                />
              </div>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <input
                  type="date"
                  className="w-full pl-10 pr-4 py-3 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring text-foreground bg-background"
                />
              </div>
            </div>
            <Button variant="hero" size="xl" className="w-full md:w-auto">
              <Search className="w-5 h-5 mr-2" />
              Rechercher
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-8 mt-12 text-center">
            <div className="animate-fade-in">
              <div className="text-3xl md:text-4xl font-bold mb-2">5000+</div>
              <div className="text-white/80">Objets disponibles</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-3xl md:text-4xl font-bold mb-2">2000+</div>
              <div className="text-white/80">Utilisateurs actifs</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-3xl md:text-4xl font-bold mb-2">15</div>
              <div className="text-white/80">Villes couvertes</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;