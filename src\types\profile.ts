export interface UserProfile {
  id: string;
  user_id: string;
  nom: string;
  prenom: string;
  email: string;
  telephone?: string;
  ville?: string;
  commune?: string;
  role: 'locataire' | 'loueur' | 'admin' | 'super_admin' | 'moderator';
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface ProfileFormData {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  ville: string;
  commune: string;
  role: 'locataire' | 'loueur';
}

export interface ProfileValidationErrors {
  nom?: string;
  prenom?: string;
  email?: string;
  telephone?: string;
  ville?: string;
  commune?: string;
  role?: string;
}

export interface Avatar {
  id: string;
  user_id: string;
  file_name: string;
  file_size: number;
  mime_type: string;
  url: string;
  created_at: string;
}

export interface AvatarUploadOptions {
  maxSize: number; // in bytes
  allowedTypes: string[];
  dimensions: {
    display: { width: number; height: number };
    storage: { width: number; height: number };
  };
}

export interface Reservation {
  id: string;
  user_id: string;
  objet_id: string;
  owner_id: string;
  start_date: string;
  end_date: string;
  total_price: number;
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled';
  message?: string;
  created_at: string;
  updated_at: string;
  // Relations
  objet?: {
    id: string;
    titre: string;
    prix_par_jour: number;
    images: string[];
    owner: {
      nom: string;
      prenom: string;
      avatar_url?: string;
    };
  };
}

export interface ReservationFormData {
  start_date: string;
  end_date: string;
  message?: string;
}

export interface ReservationValidationErrors {
  start_date?: string;
  end_date?: string;
  message?: string;
  general?: string;
}

// Utility types for form states
export type FormState = 'idle' | 'loading' | 'success' | 'error';

export interface FormResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: Record<string, string>;
}

// Constants
export const CITIES = [
  'Abidjan',
  'Bouaké',
  'Daloa',
  'Yamoussoukro',
  'San-Pédro',
  'Korhogo',
  'Man',
  'Divo',
  'Gagnoa',
  'Anyama'
] as const;

export const COMMUNES_BY_CITY: Record<string, string[]> = {
  'Abidjan': [
    'Abobo',
    'Adjamé', 
    'Attécoubé',
    'Cocody',
    'Koumassi',
    'Marcory',
    'Plateau',
    'Port-Bouët',
    'Treichville',
    'Yopougon',
    'Bingerville',
    'Songon',
    'Anyama',
    'Dabou'
  ],
  'Bouaké': ['Bouaké Centre', 'Koko', 'Gonfreville', 'Air France'],
  'Daloa': ['Daloa Centre', 'Tazibouo', 'Lobia'],
  'Yamoussoukro': ['Yamoussoukro Centre', 'Habitat', 'Millionnaire'],
  'San-Pédro': ['San-Pédro Centre', 'Bardot', 'Balmer'],
  'Korhogo': ['Korhogo Centre', 'Petit Paris', 'Résidentiel'],
  'Man': ['Man Centre', 'Libreville', 'Gbangbégouiné'],
  'Divo': ['Divo Centre', 'Hiré', 'Zépréguhé'],
  'Gagnoa': ['Gagnoa Centre', 'Dignago', 'Guibéroua'],
  'Anyama': ['Anyama Centre', 'Anonkoua-Kouté', 'Agban']
};

export const ROLE_DESCRIPTIONS = {
  locataire: {
    title: 'Locataire',
    description: 'Je souhaite louer des objets proposés par d\'autres utilisateurs'
  },
  loueur: {
    title: 'Loueur',
    description: 'Je souhaite proposer mes objets à la location et générer des revenus'
  }
} as const;

export const AVATAR_CONFIG: AvatarUploadOptions = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  dimensions: {
    display: { width: 200, height: 200 },
    storage: { width: 400, height: 400 }
  }
};

export const RESERVATION_STATUS_LABELS = {
  pending: 'En attente',
  confirmed: 'Confirmée',
  active: 'En cours',
  completed: 'Terminée',
  cancelled: 'Annulée'
} as const;

export const RESERVATION_STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirmed: 'bg-blue-100 text-blue-800',
  active: 'bg-green-100 text-green-800',
  completed: 'bg-gray-100 text-gray-800',
  cancelled: 'bg-red-100 text-red-800'
} as const;

// Notification System Types
export interface Notification {
  id: string;
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  read: boolean;
  created_at: string;
  updated_at: string;
}

export type NotificationType =
  | 'reservation_confirmed'
  | 'reservation_cancelled'
  | 'reservation_completed'
  | 'payment_received'
  | 'object_approved'
  | 'object_rejected'
  | 'system_announcement';

export const NOTIFICATION_TYPE_LABELS = {
  reservation_confirmed: "Réservation confirmée",
  reservation_cancelled: "Réservation annulée",
  reservation_completed: "Réservation terminée",
  payment_received: "Paiement reçu",
  object_approved: "Objet approuvé",
  object_rejected: "Objet rejeté",
  system_announcement: "Annonce système"
} as const;

export const NOTIFICATION_TYPE_ICONS = {
  reservation_confirmed: "check-circle",
  reservation_cancelled: "x-circle",
  reservation_completed: "star",
  payment_received: "credit-card",
  object_approved: "thumbs-up",
  object_rejected: "thumbs-down",
  system_announcement: "bell"
} as const;
