import { MockUser } from './types';

// Noms ivoiriens réalistes
const PRENOMS_MASCULINS = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>'
];

const PRENOMS_FEMININS = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  'Am<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
];

const NOMS_FAMILLE = [
  '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>O<PERSON>A<PERSON><PERSON>', 'OUATTARA', 'TRAORE', 'BROU',
  'ASSI', 'DIALLO', 'SANGARE', 'COULIBALY', 'DIABATE', 'DOUMBIA', 'FOFANA',
  'TOURE', 'CAMARA', 'KEITA', '<PERSON>YLLA', 'BARRY', 'SOW', 'DIOP', 'FALL',
  'NDIAYE', 'GUEYE', 'DIOUF', 'SARR', 'WADE', 'LO', 'NDOUR'
];

const VILLES_COMMUNES = {
  'Abidjan': ['Cocody', 'Plateau', 'Marcory', 'Treichville', 'Adjamé', 'Yopougon', 'Abobo', 'Koumassi', 'Port-Bouët', 'Attécoubé', 'Bingerville', 'Songon', 'Anyama'],
  'Bouaké': ['Koko', 'Dar-Es-Salam', 'Kennedy', 'Belleville', 'Nimbo'],
  'Daloa': ['Centre', 'Tazibouo', 'Lobia'],
  'Yamoussoukro': ['Centre', 'Habitat', 'Millionnaire', 'N\'Zuessy'],
  'San-Pédro': ['Centre', 'Bardot', 'Balmer'],
  'Korhogo': ['Centre', 'Petit-Paris', 'Résidentiel'],
  'Man': ['Centre', 'Libreville', 'Gbangbégouiné'],
  'Divo': ['Centre', 'Hiré', 'Zépréguhé'],
  'Gagnoa': ['Centre', 'Dignago', 'Guibéroua'],
  'Abengourou': ['Centre', 'Aniassué', 'Zaranou']
};

// Génération d'avatars réalistes
const generateAvatarUrl = (prenom: string, nom: string, genre: 'homme' | 'femme'): string => {
  const seed = `${prenom}-${nom}`.toLowerCase();
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}&backgroundColor=b6e3f4,c0aede,d1d4f9&clothesColor=262e33,65c9ff,5199e4,25557c&eyeColor=brown,blue,green&hairColor=auburn,black,blonde,brown&skinColor=light,pale,tanned`;
};

// Génération des bios réalistes
const BIOS_TEMPLATES = [
  "Passionné de bricolage et toujours prêt à aider mes voisins avec mes outils.",
  "Photographe amateur qui loue son matériel pour partager ma passion.",
  "Étudiant en génie civil, je loue mes équipements pour financer mes études.",
  "Propriétaire d'une petite entreprise de location d'équipements.",
  "Retraité qui met à disposition ses outils de jardinage.",
  "Mère de famille qui loue occasionnellement ses appareils électroménagers.",
  "Entrepreneur dans le BTP, je partage mes équipements professionnels.",
  "Passionné de technologie, je loue mes gadgets électroniques.",
  "Artisan menuisier qui met ses outils à disposition de la communauté.",
  "Étudiante qui loue ses affaires pour arrondir ses fins de mois."
];

// Fonction pour générer un utilisateur aléatoire
const generateRandomUser = (id: string, role: MockUser['role'] = 'locataire'): MockUser => {
  const isHomme = Math.random() > 0.5;
  const prenom = isHomme 
    ? PRENOMS_MASCULINS[Math.floor(Math.random() * PRENOMS_MASCULINS.length)]
    : PRENOMS_FEMININS[Math.floor(Math.random() * PRENOMS_FEMININS.length)];
  
  const nom = NOMS_FAMILLE[Math.floor(Math.random() * NOMS_FAMILLE.length)];
  const villes = Object.keys(VILLES_COMMUNES);
  const ville = villes[Math.floor(Math.random() * villes.length)];
  const communes = VILLES_COMMUNES[ville];
  const commune = communes[Math.floor(Math.random() * communes.length)];
  
  const email = `${prenom.toLowerCase()}.${nom.toLowerCase()}@email.ci`;
  const telephone = `+225 ${Math.floor(Math.random() * 9) + 1}${Math.floor(Math.random() * 9)}${Math.floor(Math.random() * 9)}${Math.floor(Math.random() * 9)}${Math.floor(Math.random() * 9)}${Math.floor(Math.random() * 9)}${Math.floor(Math.random() * 9)}${Math.floor(Math.random() * 9)}`;
  
  const noteBase = role === 'loueur' ? 4.2 : 4.5;
  const note = Math.round((noteBase + Math.random() * 0.8) * 10) / 10;
  const nombreAvis = role === 'loueur' ? Math.floor(Math.random() * 50) + 5 : Math.floor(Math.random() * 20) + 1;
  
  const dateInscription = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  const derniereConnexion = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
  
  return {
    id,
    user_id: id,
    email,
    nom,
    prenom,
    telephone,
    ville,
    commune,
    role,
    avatar_url: generateAvatarUrl(prenom, nom, isHomme ? 'homme' : 'femme'),
    note_moyenne: note,
    nombre_avis: nombreAvis,
    date_inscription: dateInscription.toISOString(),
    derniere_connexion: derniereConnexion.toISOString(),
    verified: Math.random() > 0.2, // 80% des utilisateurs sont vérifiés
    bio: role === 'loueur' ? BIOS_TEMPLATES[Math.floor(Math.random() * BIOS_TEMPLATES.length)] : undefined
  };
};

// Utilisateurs prédéfinis pour les tests
export const MOCK_USERS: MockUser[] = [
  // Utilisateur admin
  {
    id: 'admin-1',
    user_id: 'admin-1',
    email: '<EMAIL>',
    nom: 'KOUASSI',
    prenom: 'Jean',
    telephone: '+225 07123456',
    ville: 'Abidjan',
    commune: 'Plateau',
    role: 'admin',
    avatar_url: generateAvatarUrl('Jean', 'KOUASSI', 'homme'),
    note_moyenne: 5.0,
    nombre_avis: 0,
    date_inscription: '2023-01-01T00:00:00.000Z',
    derniere_connexion: new Date().toISOString(),
    verified: true,
    bio: 'Administrateur de la plateforme AfroRent Hub'
  },
  
  // Utilisateur test pour connexion
  {
    id: 'user-test',
    user_id: 'user-test',
    email: '<EMAIL>',
    nom: 'KONE',
    prenom: 'Moussa',
    telephone: '+225 07654321',
    ville: 'Abidjan',
    commune: 'Cocody',
    role: 'loueur',
    avatar_url: generateAvatarUrl('Moussa', 'KONE', 'homme'),
    note_moyenne: 4.8,
    nombre_avis: 23,
    date_inscription: '2023-06-15T00:00:00.000Z',
    derniere_connexion: new Date().toISOString(),
    verified: true,
    bio: 'Propriétaire d\'une petite entreprise de location d\'équipements. Toujours disponible pour aider!'
  },

  // Génération d'utilisateurs aléatoires
  ...Array.from({ length: 50 }, (_, i) => {
    const roles: MockUser['role'][] = ['locataire', 'loueur', 'loueur', 'locataire', 'locataire']; // Plus de locataires
    const role = roles[i % roles.length];
    return generateRandomUser(`user-${i + 1}`, role);
  })
];

// Fonction pour obtenir un utilisateur par ID
export const getUserById = (id: string): MockUser | undefined => {
  return MOCK_USERS.find(user => user.id === id || user.user_id === id);
};

// Fonction pour obtenir des utilisateurs par ville
export const getUsersByVille = (ville: string): MockUser[] => {
  return MOCK_USERS.filter(user => user.ville === ville);
};

// Fonction pour obtenir des loueurs
export const getLoueurs = (): MockUser[] => {
  return MOCK_USERS.filter(user => user.role === 'loueur');
};

// Fonction pour obtenir des utilisateurs par note
export const getUsersByNote = (noteMin: number = 4.0): MockUser[] => {
  return MOCK_USERS.filter(user => user.note_moyenne >= noteMin);
};

// Fonction de recherche d'utilisateurs
export const searchUsers = (query: string): MockUser[] => {
  const searchTerm = query.toLowerCase();
  return MOCK_USERS.filter(user => 
    user.nom.toLowerCase().includes(searchTerm) ||
    user.prenom.toLowerCase().includes(searchTerm) ||
    user.ville.toLowerCase().includes(searchTerm) ||
    user.email.toLowerCase().includes(searchTerm)
  );
};

export default MOCK_USERS;
