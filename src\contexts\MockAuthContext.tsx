import React, { createContext, useContext, useEffect, useState } from 'react';
import { MockService } from '@/data/mockData/mockService';
import { MockUser } from '@/data/mockData/types';

// Utilisation du type MockUser
type Profile = MockUser;

// Interface simplifiée pour l'utilisateur mock
interface MockUserSession {
  id: string;
  email: string;
}

interface AuthContextType {
  user: MockUserSession | null;
  session: MockUserSession | null;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData: Partial<Profile>) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<MockUserSession | null>(null);
  const [session, setSession] = useState<MockUserSession | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchProfile = async (userId: string) => {
    try {
      const userData = await MockService.getUserById(userId);
      if (userData) {
        setProfile(userData);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération du profil:', error);
    }
  };

  useEffect(() => {
    // Vérifier s'il y a une session stockée localement
    const checkStoredSession = async () => {
      try {
        const storedUser = localStorage.getItem('afrorent_user');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          const mockSession: MockUserSession = {
            id: userData.id,
            email: userData.email
          };
          
          setUser(mockSession);
          setSession(mockSession);
          await fetchProfile(userData.id);
        }
      } catch (error) {
        console.error('Erreur lors de la vérification de la session:', error);
        localStorage.removeItem('afrorent_user');
      } finally {
        setLoading(false);
      }
    };

    checkStoredSession();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      const result = await MockService.signIn(email, password);
      
      if (result.error) {
        return { error: result.error };
      }
      
      if (result.user) {
        const mockSession: MockUserSession = {
          id: result.user.id,
          email: result.user.email
        };
        
        setUser(mockSession);
        setSession(mockSession);
        setProfile(result.user);
        
        // Stocker la session localement
        localStorage.setItem('afrorent_user', JSON.stringify(result.user));
      }
      
      return { error: null };
    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      return { error: 'Erreur lors de la connexion' };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, userData: Partial<Profile>) => {
    try {
      setLoading(true);
      
      const result = await MockService.signUp(email, password, userData);
      
      if (result.error) {
        return { error: result.error };
      }
      
      if (result.user) {
        const mockSession: MockUserSession = {
          id: result.user.id,
          email: result.user.email
        };
        
        setUser(mockSession);
        setSession(mockSession);
        setProfile(result.user);
        
        // Stocker la session localement
        localStorage.setItem('afrorent_user', JSON.stringify(result.user));
      }
      
      return { error: null };
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      return { error: 'Erreur lors de l\'inscription' };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setUser(null);
      setSession(null);
      setProfile(null);
      
      // Supprimer la session locale
      localStorage.removeItem('afrorent_user');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      if (!profile) {
        return { error: 'Aucun profil à mettre à jour' };
      }
      
      // Simuler la mise à jour du profil
      const updatedProfile = { ...profile, ...updates };
      setProfile(updatedProfile);
      
      // Mettre à jour la session locale
      localStorage.setItem('afrorent_user', JSON.stringify(updatedProfile));
      
      return { error: null };
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      return { error: 'Erreur lors de la mise à jour du profil' };
    }
  };

  const value = {
    user,
    session,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthProvider;
