import React, { useState, useRef } from 'react';
import { useAuth } from '@/contexts/MockAuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { Camera, Upload, X, Check, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AVATAR_CONFIG } from '@/types/profile';

interface AvatarUploadProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showUploadButton?: boolean;
  className?: string;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({ 
  size = 'lg', 
  showUploadButton = true,
  className 
}) => {
  const { user, profile, updateProfile } = useAuth();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-20 w-20',
    xl: 'h-32 w-32'
  };

  const buttonSizes = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10',
    xl: 'h-12 w-12'
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!AVATAR_CONFIG.allowedTypes.includes(file.type)) {
      return 'Format de fichier non supporté. Utilisez JPEG, PNG ou WebP.';
    }

    // Check file size
    if (file.size > AVATAR_CONFIG.maxSize) {
      return `Le fichier est trop volumineux. Taille maximum: ${AVATAR_CONFIG.maxSize / (1024 * 1024)}MB.`;
    }

    return null;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const error = validateFile(file);
    if (error) {
      toast({
        title: "Erreur de fichier",
        description: error,
        variant: "destructive"
      });
      return;
    }

    setSelectedFile(file);
    
    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile || !user) return;

    setIsUploading(true);

    try {
      // Create a unique filename
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `${user.id}-${Date.now()}.${fileExt}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, selectedFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      // Update profile with new avatar URL
      await updateProfile({ avatar_url: urlData.publicUrl });

      toast({
        title: "Avatar mis à jour",
        description: "Votre photo de profil a été mise à jour avec succès"
      });

      // Reset state
      setSelectedFile(null);
      setPreviewUrl(null);
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      toast({
        title: "Erreur de téléchargement",
        description: "Une erreur s'est produite lors du téléchargement de l'image",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  const getInitials = () => {
    if (profile?.prenom && profile?.nom) {
      return `${profile.prenom[0]}${profile.nom[0]}`.toUpperCase();
    }
    return profile?.email?.[0]?.toUpperCase() || 'U';
  };

  const currentAvatarUrl = previewUrl || profile?.avatar_url;

  return (
    <div className={cn("flex flex-col items-center space-y-3", className)}>
      {/* Avatar Display */}
      <div className="relative">
        <Avatar className={cn(sizeClasses[size], "ring-2 ring-border")}>
          {currentAvatarUrl ? (
            <AvatarImage 
              src={currentAvatarUrl} 
              alt="Avatar" 
              className="object-cover"
            />
          ) : (
            <AvatarFallback className="bg-primary text-primary-foreground text-lg font-semibold">
              {getInitials()}
            </AvatarFallback>
          )}
        </Avatar>

        {/* Upload button overlay */}
        {showUploadButton && !selectedFile && (
          <Button
            size="icon"
            variant="secondary"
            className={cn(
              "absolute -bottom-1 -right-1 rounded-full shadow-lg",
              buttonSizes[size]
            )}
            onClick={triggerFileSelect}
            disabled={isUploading}
          >
            <Camera className="h-3 w-3" />
          </Button>
        )}

        {/* Loading overlay */}
        {isUploading && (
          <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
            <Loader2 className="h-4 w-4 text-white animate-spin" />
          </div>
        )}
      </div>

      {/* File input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={AVATAR_CONFIG.allowedTypes.join(',')}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Upload controls */}
      {selectedFile && (
        <div className="flex items-center space-x-2 animate-slide-up">
          <Button
            size="sm"
            onClick={handleUpload}
            disabled={isUploading}
            className="flex items-center space-x-1"
          >
            {isUploading ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Check className="h-3 w-3" />
            )}
            <span>{isUploading ? 'Téléchargement...' : 'Confirmer'}</span>
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleCancel}
            disabled={isUploading}
          >
            <X className="h-3 w-3 mr-1" />
            Annuler
          </Button>
        </div>
      )}

      {/* Upload button (alternative) */}
      {showUploadButton && !selectedFile && size === 'xl' && (
        <Button
          variant="outline"
          size="sm"
          onClick={triggerFileSelect}
          disabled={isUploading}
          className="flex items-center space-x-2"
        >
          <Upload className="h-4 w-4" />
          <span>Changer la photo</span>
        </Button>
      )}

      {/* File info */}
      {selectedFile && (
        <div className="text-xs text-muted-foreground text-center">
          <p>{selectedFile.name}</p>
          <p>{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
        </div>
      )}
    </div>
  );
};

export default AvatarUpload;
