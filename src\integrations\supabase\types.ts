export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      avis: {
        Row: {
          auteur_id: string
          commentaire: string | null
          created_at: string | null
          id: string
          note: number
          objet_id: string
          reservation_id: string
        }
        Insert: {
          auteur_id: string
          commentaire?: string | null
          created_at?: string | null
          id?: string
          note: number
          objet_id: string
          reservation_id: string
        }
        Update: {
          auteur_id?: string
          commentaire?: string | null
          created_at?: string | null
          id?: string
          note?: number
          objet_id?: string
          reservation_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "avis_auteur_id_fkey"
            columns: ["auteur_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "avis_objet_id_fkey"
            columns: ["objet_id"]
            isOneToOne: false
            referencedRelation: "objets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "avis_reservation_id_fkey"
            columns: ["reservation_id"]
            isOneToOne: false
            referencedRelation: "reservations"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          icone: string | null
          id: string
          nom: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          icone?: string | null
          id?: string
          nom: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          icone?: string | null
          id?: string
          nom?: string
        }
        Relationships: []
      }
      disponibilites: {
        Row: {
          created_at: string | null
          date_debut: string
          date_fin: string
          disponible: boolean | null
          id: string
          objet_id: string
        }
        Insert: {
          created_at?: string | null
          date_debut: string
          date_fin: string
          disponible?: boolean | null
          id?: string
          objet_id: string
        }
        Update: {
          created_at?: string | null
          date_debut?: string
          date_fin?: string
          disponible?: boolean | null
          id?: string
          objet_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "disponibilites_objet_id_fkey"
            columns: ["objet_id"]
            isOneToOne: false
            referencedRelation: "objets"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          contenu: string
          created_at: string | null
          destinataire_id: string
          expediteur_id: string
          id: string
          lu: boolean | null
          reservation_id: string
        }
        Insert: {
          contenu: string
          created_at?: string | null
          destinataire_id: string
          expediteur_id: string
          id?: string
          lu?: boolean | null
          reservation_id: string
        }
        Update: {
          contenu?: string
          created_at?: string | null
          destinataire_id?: string
          expediteur_id?: string
          id?: string
          lu?: boolean | null
          reservation_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_destinataire_id_fkey"
            columns: ["destinataire_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_expediteur_id_fkey"
            columns: ["expediteur_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_reservation_id_fkey"
            columns: ["reservation_id"]
            isOneToOne: false
            referencedRelation: "reservations"
            referencedColumns: ["id"]
          },
        ]
      }
      objets: {
        Row: {
          categorie_id: string | null
          caution: number | null
          conditions: string | null
          created_at: string | null
          description: string
          disponible: boolean | null
          id: string
          images: string[] | null
          localisation_commune: string | null
          localisation_ville: string | null
          prix_par_jour: number
          proprietaire_id: string
          titre: string
          updated_at: string | null
        }
        Insert: {
          categorie_id?: string | null
          caution?: number | null
          conditions?: string | null
          created_at?: string | null
          description: string
          disponible?: boolean | null
          id?: string
          images?: string[] | null
          localisation_commune?: string | null
          localisation_ville?: string | null
          prix_par_jour: number
          proprietaire_id: string
          titre: string
          updated_at?: string | null
        }
        Update: {
          categorie_id?: string | null
          caution?: number | null
          conditions?: string | null
          created_at?: string | null
          description?: string
          disponible?: boolean | null
          id?: string
          images?: string[] | null
          localisation_commune?: string | null
          localisation_ville?: string | null
          prix_par_jour?: number
          proprietaire_id?: string
          titre?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objets_categorie_id_fkey"
            columns: ["categorie_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "objets_proprietaire_id_fkey"
            columns: ["proprietaire_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          commune: string | null
          created_at: string | null
          email: string
          id: string
          nom: string | null
          prenom: string | null
          role: string | null
          telephone: string | null
          updated_at: string | null
          user_id: string
          ville: string | null
        }
        Insert: {
          avatar_url?: string | null
          commune?: string | null
          created_at?: string | null
          email: string
          id?: string
          nom?: string | null
          prenom?: string | null
          role?: string | null
          telephone?: string | null
          updated_at?: string | null
          user_id: string
          ville?: string | null
        }
        Update: {
          avatar_url?: string | null
          commune?: string | null
          created_at?: string | null
          email?: string
          id?: string
          nom?: string | null
          prenom?: string | null
          role?: string | null
          telephone?: string | null
          updated_at?: string | null
          user_id?: string
          ville?: string | null
        }
        Relationships: []
      }
      reservations: {
        Row: {
          caution: number | null
          created_at: string | null
          date_debut: string
          date_fin: string
          id: string
          locataire_id: string
          loueur_id: string
          message_locataire: string | null
          objet_id: string
          prix_total: number
          statut: string | null
          stripe_session_id: string | null
          updated_at: string | null
        }
        Insert: {
          caution?: number | null
          created_at?: string | null
          date_debut: string
          date_fin: string
          id?: string
          locataire_id: string
          loueur_id: string
          message_locataire?: string | null
          objet_id: string
          prix_total: number
          statut?: string | null
          stripe_session_id?: string | null
          updated_at?: string | null
        }
        Update: {
          caution?: number | null
          created_at?: string | null
          date_debut?: string
          date_fin?: string
          id?: string
          locataire_id?: string
          loueur_id?: string
          message_locataire?: string | null
          objet_id?: string
          prix_total?: number
          statut?: string | null
          stripe_session_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reservations_locataire_id_fkey"
            columns: ["locataire_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reservations_loueur_id_fkey"
            columns: ["loueur_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reservations_objet_id_fkey"
            columns: ["objet_id"]
            isOneToOne: false
            referencedRelation: "objets"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
