// Script de diagnostic pour vérifier les imports et dépendances

export const runDiagnostics = () => {
  console.log('🔍 DIAGNOSTIC AFRORENT HUB');
  console.log('============================');

  // Test 1: Vérifier les imports de base
  try {
    console.log('✅ Test 1: Imports React de base');
    console.log('   - React:', typeof React !== 'undefined' ? '✅' : '❌');
    console.log('   - useState:', typeof React.useState !== 'undefined' ? '✅' : '❌');
    console.log('   - useEffect:', typeof React.useEffect !== 'undefined' ? '✅' : '❌');
  } catch (error) {
    console.error('❌ Test 1 échoué:', error);
  }

  // Test 2: Vérifier les composants UI
  try {
    console.log('\n✅ Test 2: Composants UI');
    
    // Test des imports dynamiques
    const testUIComponents = async () => {
      try {
        const { Button } = await import('@/components/ui/button');
        console.log('   - Button:', Button ? '✅' : '❌');
      } catch (e) {
        console.log('   - Button: ❌', e.message);
      }

      try {
        const { Card } = await import('@/components/ui/card');
        console.log('   - Card:', Card ? '✅' : '❌');
      } catch (e) {
        console.log('   - Card: ❌', e.message);
      }

      try {
        const { Slider } = await import('@/components/ui/slider');
        console.log('   - Slider:', Slider ? '✅' : '❌');
      } catch (e) {
        console.log('   - Slider: ❌', e.message);
      }

      try {
        const { Checkbox } = await import('@/components/ui/checkbox');
        console.log('   - Checkbox:', Checkbox ? '✅' : '❌');
      } catch (e) {
        console.log('   - Checkbox: ❌', e.message);
      }
    };

    testUIComponents();
  } catch (error) {
    console.error('❌ Test 2 échoué:', error);
  }

  // Test 3: Vérifier les données
  try {
    console.log('\n✅ Test 3: Données de l\'application');
    
    const testData = async () => {
      try {
        const { IVORIAN_CITIES } = await import('@/data/locations');
        console.log('   - Villes ivoiriennes:', IVORIAN_CITIES?.length > 0 ? `✅ (${IVORIAN_CITIES.length} villes)` : '❌');
      } catch (e) {
        console.log('   - Villes ivoiriennes: ❌', e.message);
      }

      try {
        const { OBJECT_CATEGORIES } = await import('@/data/categories');
        console.log('   - Catégories d\'objets:', OBJECT_CATEGORIES?.length > 0 ? `✅ (${OBJECT_CATEGORIES.length} catégories)` : '❌');
      } catch (e) {
        console.log('   - Catégories d\'objets: ❌', e.message);
      }
    };

    testData();
  } catch (error) {
    console.error('❌ Test 3 échoué:', error);
  }

  // Test 4: Vérifier les hooks
  try {
    console.log('\n✅ Test 4: Hooks personnalisés');
    
    const testHooks = async () => {
      try {
        const { useAdmin } = await import('@/hooks/useAdmin');
        console.log('   - useAdmin:', useAdmin ? '✅' : '❌');
      } catch (e) {
        console.log('   - useAdmin: ❌', e.message);
      }

      try {
        const { useObjects } = await import('@/hooks/useObjects');
        console.log('   - useObjects:', useObjects ? '✅' : '❌');
      } catch (e) {
        console.log('   - useObjects: ❌', e.message);
      }
    };

    testHooks();
  } catch (error) {
    console.error('❌ Test 4 échoué:', error);
  }

  // Test 5: Vérifier les composants personnalisés
  try {
    console.log('\n✅ Test 5: Composants personnalisés');
    
    const testCustomComponents = async () => {
      try {
        const ExplorerFilters = await import('@/components/ExplorerFilters');
        console.log('   - ExplorerFilters:', ExplorerFilters.default ? '✅' : '❌');
      } catch (e) {
        console.log('   - ExplorerFilters: ❌', e.message);
      }

      try {
        const AdminLayout = await import('@/components/AdminLayout');
        console.log('   - AdminLayout:', AdminLayout.default ? '✅' : '❌');
      } catch (e) {
        console.log('   - AdminLayout: ❌', e.message);
      }

      try {
        const NotificationBell = await import('@/components/NotificationBell');
        console.log('   - NotificationBell:', NotificationBell.default ? '✅' : '❌');
      } catch (e) {
        console.log('   - NotificationBell: ❌', e.message);
      }
    };

    testCustomComponents();
  } catch (error) {
    console.error('❌ Test 5 échoué:', error);
  }

  // Test 6: Vérifier les utilitaires
  try {
    console.log('\n✅ Test 6: Utilitaires');
    
    const testUtils = async () => {
      try {
        const { formatCurrency } = await import('@/utils/profileValidation');
        console.log('   - formatCurrency:', formatCurrency ? '✅' : '❌');
        
        // Test de la fonction
        const testAmount = formatCurrency(25000);
        console.log('   - Test formatCurrency(25000):', testAmount ? `✅ (${testAmount})` : '❌');
      } catch (e) {
        console.log('   - formatCurrency: ❌', e.message);
      }

      try {
        const { cn } = await import('@/lib/utils');
        console.log('   - cn (className utility):', cn ? '✅' : '❌');
      } catch (e) {
        console.log('   - cn: ❌', e.message);
      }
    };

    testUtils();
  } catch (error) {
    console.error('❌ Test 6 échoué:', error);
  }

  // Test 7: Vérifier les icônes Lucide
  try {
    console.log('\n✅ Test 7: Icônes Lucide React');
    
    const testIcons = async () => {
      try {
        const { Bell, Check, Filter, Search } = await import('lucide-react');
        console.log('   - Bell:', Bell ? '✅' : '❌');
        console.log('   - Check:', Check ? '✅' : '❌');
        console.log('   - Filter:', Filter ? '✅' : '❌');
        console.log('   - Search:', Search ? '✅' : '❌');
      } catch (e) {
        console.log('   - Icônes Lucide: ❌', e.message);
      }
    };

    testIcons();
  } catch (error) {
    console.error('❌ Test 7 échoué:', error);
  }

  console.log('\n🎯 DIAGNOSTIC TERMINÉ');
  console.log('=====================');
  console.log('Vérifiez les résultats ci-dessus pour identifier les problèmes.');
  console.log('Les éléments marqués ❌ nécessitent une attention particulière.');
};

// Instructions d'utilisation :
console.log(`
🔍 SCRIPT DE DIAGNOSTIC AFRORENT HUB

Pour exécuter le diagnostic :
1. Ouvrez la console du navigateur (F12)
2. Exécutez : runDiagnostics()
3. Analysez les résultats pour identifier les problèmes
`);

// Export pour utilisation dans d'autres fichiers
export default runDiagnostics;
