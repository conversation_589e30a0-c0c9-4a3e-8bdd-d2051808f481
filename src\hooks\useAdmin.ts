import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import {
  AdminUser,
  AdminUserOverview,
  AdminObjectOverview,
  AdminReservationOverview,
  UserStats,
  ObjectStats,
  ReservationStats,
  AdminFilters,
  PaginationInfo,
  AdminApiResponse,
  AdminLog
} from '@/types/admin';

interface UseAdminReturn {
  // Auth & Permissions
  isAdmin: boolean;
  adminUser: AdminUser | null;
  hasPermission: (permission: string) => boolean;
  
  // Users Management
  users: AdminUserOverview[];
  userStats: UserStats | null;
  loadUsers: (filters?: AdminFilters) => Promise<void>;
  suspendUser: (userId: string, reason: string) => Promise<void>;
  activateUser: (userId: string) => Promise<void>;
  verifyUser: (userId: string) => Promise<void>;
  rejectUser: (userId: string, reason: string) => Promise<void>;
  
  // Objects Management
  objects: AdminObjectOverview[];
  objectStats: ObjectStats | null;
  loadObjects: (filters?: AdminFilters) => Promise<void>;
  approveObject: (objectId: string) => Promise<void>;
  rejectObject: (objectId: string, reason: string) => Promise<void>;
  
  // Reservations Management
  reservations: AdminReservationOverview[];
  reservationStats: ReservationStats | null;
  loadReservations: (filters?: AdminFilters) => Promise<void>;
  
  // Audit Logs
  logs: AdminLog[];
  loadLogs: (filters?: AdminFilters) => Promise<void>;
  logAction: (action: string, resourceType: string, resourceId: string, details?: any) => Promise<void>;
  
  // Loading States
  loading: boolean;
  usersLoading: boolean;
  objectsLoading: boolean;
  reservationsLoading: boolean;
  
  // Pagination
  pagination: PaginationInfo | null;
  
  // Error Handling
  error: string | null;
}

export const useAdmin = (): UseAdminReturn => {
  const { user, profile } = useAuth();
  const { toast } = useToast();
  
  // State
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [users, setUsers] = useState<AdminUserOverview[]>([]);
  const [objects, setObjects] = useState<AdminObjectOverview[]>([]);
  const [reservations, setReservations] = useState<AdminReservationOverview[]>([]);
  const [logs, setLogs] = useState<AdminLog[]>([]);
  
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [objectStats, setObjectStats] = useState<ObjectStats | null>(null);
  const [reservationStats, setReservationStats] = useState<ReservationStats | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);
  const [objectsLoading, setObjectsLoading] = useState(false);
  const [reservationsLoading, setReservationsLoading] = useState(false);
  
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Check if user is admin
  const isAdmin = profile?.role === 'admin' || profile?.role === 'super_admin' || profile?.role === 'moderator';

  // Initialize admin user
  useEffect(() => {
    if (isAdmin && profile) {
      setAdminUser({
        id: profile.id,
        email: profile.email || '',
        nom: profile.nom || '',
        prenom: profile.prenom || '',
        role: profile.role as 'admin' | 'super_admin' | 'moderator',
        permissions: getPermissionsForRole(profile.role),
        created_at: profile.created_at || '',
        last_login: new Date().toISOString(),
        is_active: true
      });
    }
  }, [isAdmin, profile]);

  // Get permissions based on role
  const getPermissionsForRole = (role: string) => {
    const permissions = [];
    
    if (role === 'super_admin') {
      permissions.push(
        'users.view', 'users.edit', 'users.suspend',
        'objects.view', 'objects.approve', 'objects.reject',
        'reservations.view', 'reservations.manage',
        'content.moderate', 'analytics.view', 'system.configure',
        'reports.export'
      );
    } else if (role === 'admin') {
      permissions.push(
        'users.view', 'users.edit', 'users.suspend',
        'objects.view', 'objects.approve', 'objects.reject',
        'reservations.view', 'reservations.manage',
        'content.moderate', 'analytics.view'
      );
    } else if (role === 'moderator') {
      permissions.push(
        'users.view', 'objects.view', 'objects.approve', 'objects.reject',
        'content.moderate'
      );
    }
    
    return permissions;
  };

  // Check permission
  const hasPermission = useCallback((permission: string): boolean => {
    return adminUser?.permissions.includes(permission as any) || false;
  }, [adminUser]);

  // Log admin action
  const logAction = useCallback(async (
    action: string,
    resourceType: string,
    resourceId: string,
    details?: any
  ) => {
    if (!adminUser) return;

    try {
      const logEntry = {
        admin_id: adminUser.id,
        admin_name: `${adminUser.prenom} ${adminUser.nom}`,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        details: details || {},
        ip_address: 'unknown', // TODO: Get real IP
        user_agent: navigator.userAgent,
        created_at: new Date().toISOString()
      };

      // TODO: Save to admin_logs table
      console.log('Admin action logged:', logEntry);
    } catch (error) {
      console.error('Failed to log admin action:', error);
    }
  }, [adminUser]);

  // Load users
  const loadUsers = useCallback(async (filters?: AdminFilters) => {
    if (!hasPermission('users.view')) return;
    
    setUsersLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      const mockUsers: AdminUserOverview[] = [
        {
          id: '1',
          email: '<EMAIL>',
          nom: 'Kouassi',
          prenom: 'Jean',
          role: 'locataire',
          verification_status: 'verified',
          account_status: 'active',
          created_at: '2024-01-15T10:00:00Z',
          last_login: '2024-07-14T08:30:00Z',
          total_reservations: 5,
          total_objects: 0,
          total_revenue: 0
        },
        {
          id: '2',
          email: '<EMAIL>',
          nom: 'Traore',
          prenom: 'Marie',
          role: 'loueur',
          verification_status: 'pending',
          account_status: 'active',
          created_at: '2024-02-20T14:00:00Z',
          last_login: '2024-07-13T16:45:00Z',
          total_reservations: 2,
          total_objects: 3,
          total_revenue: 45000
        }
      ];

      const mockStats: UserStats = {
        total_users: 1250,
        new_users_today: 12,
        new_users_this_week: 89,
        new_users_this_month: 342,
        active_users: 1180,
        suspended_users: 15,
        verified_users: 980,
        pending_verification: 255
      };

      setUsers(mockUsers);
      setUserStats(mockStats);
      
      // Mock pagination
      setPagination({
        current_page: 1,
        total_pages: 63,
        total_items: 1250,
        items_per_page: 20,
        has_next: true,
        has_previous: false
      });

    } catch (error) {
      setError('Erreur lors du chargement des utilisateurs');
      toast({
        title: "Erreur",
        description: "Impossible de charger les utilisateurs",
        variant: "destructive"
      });
    } finally {
      setUsersLoading(false);
    }
  }, [hasPermission, toast]);

  // User management actions
  const suspendUser = useCallback(async (userId: string, reason: string) => {
    if (!hasPermission('users.suspend')) return;

    try {
      // TODO: API call to suspend user
      await logAction('suspend_user', 'user', userId, { reason });
      
      toast({
        title: "Utilisateur suspendu",
        description: "L'utilisateur a été suspendu avec succès"
      });
      
      // Reload users
      await loadUsers();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de suspendre l'utilisateur",
        variant: "destructive"
      });
    }
  }, [hasPermission, logAction, loadUsers, toast]);

  const activateUser = useCallback(async (userId: string) => {
    if (!hasPermission('users.edit')) return;

    try {
      // TODO: API call to activate user
      await logAction('activate_user', 'user', userId);
      
      toast({
        title: "Utilisateur activé",
        description: "L'utilisateur a été activé avec succès"
      });
      
      await loadUsers();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible d'activer l'utilisateur",
        variant: "destructive"
      });
    }
  }, [hasPermission, logAction, loadUsers, toast]);

  const verifyUser = useCallback(async (userId: string) => {
    if (!hasPermission('users.edit')) return;

    try {
      // TODO: API call to verify user
      await logAction('verify_user', 'user', userId);
      
      toast({
        title: "Utilisateur vérifié",
        description: "L'utilisateur a été vérifié avec succès"
      });
      
      await loadUsers();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de vérifier l'utilisateur",
        variant: "destructive"
      });
    }
  }, [hasPermission, logAction, loadUsers, toast]);

  const rejectUser = useCallback(async (userId: string, reason: string) => {
    if (!hasPermission('users.edit')) return;

    try {
      // TODO: API call to reject user
      await logAction('reject_user', 'user', userId, { reason });
      
      toast({
        title: "Utilisateur rejeté",
        description: "La vérification de l'utilisateur a été rejetée"
      });
      
      await loadUsers();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de rejeter l'utilisateur",
        variant: "destructive"
      });
    }
  }, [hasPermission, logAction, loadUsers, toast]);

  // Load objects (placeholder)
  const loadObjects = useCallback(async (filters?: AdminFilters) => {
    if (!hasPermission('objects.view')) return;
    setObjectsLoading(true);
    // TODO: Implement
    setObjectsLoading(false);
  }, [hasPermission]);

  // Object management actions (placeholders)
  const approveObject = useCallback(async (objectId: string) => {
    if (!hasPermission('objects.approve')) return;
    // TODO: Implement
  }, [hasPermission]);

  const rejectObject = useCallback(async (objectId: string, reason: string) => {
    if (!hasPermission('objects.reject')) return;
    // TODO: Implement
  }, [hasPermission]);

  // Load reservations (placeholder)
  const loadReservations = useCallback(async (filters?: AdminFilters) => {
    if (!hasPermission('reservations.view')) return;
    setReservationsLoading(true);
    // TODO: Implement
    setReservationsLoading(false);
  }, [hasPermission]);

  // Load logs (placeholder)
  const loadLogs = useCallback(async (filters?: AdminFilters) => {
    setLoading(true);
    // TODO: Implement
    setLoading(false);
  }, []);

  return {
    // Auth & Permissions
    isAdmin,
    adminUser,
    hasPermission,
    
    // Users Management
    users,
    userStats,
    loadUsers,
    suspendUser,
    activateUser,
    verifyUser,
    rejectUser,
    
    // Objects Management
    objects,
    objectStats,
    loadObjects,
    approveObject,
    rejectObject,
    
    // Reservations Management
    reservations,
    reservationStats,
    loadReservations,
    
    // Audit Logs
    logs,
    loadLogs,
    logAction,
    
    // Loading States
    loading,
    usersLoading,
    objectsLoading,
    reservationsLoading,
    
    // Pagination
    pagination,
    
    // Error Handling
    error
  };
};
