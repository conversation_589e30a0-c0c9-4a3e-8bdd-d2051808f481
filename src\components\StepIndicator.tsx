import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Step {
  id: number;
  title: string;
  description?: string;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
  className?: string;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ 
  steps, 
  currentStep, 
  className 
}) => {
  return (
    <div className={cn("w-full", className)}>
      {/* Progress bar */}
      <div className="relative mb-6">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const stepNumber = index + 1;
            const isCompleted = stepNumber < currentStep;
            const isCurrent = stepNumber === currentStep;
            const isUpcoming = stepNumber > currentStep;

            return (
              <div key={step.id} className="flex flex-col items-center relative">
                {/* Step circle */}
                <div
                  className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300 relative z-10",
                    isCompleted && "bg-primary text-primary-foreground scale-110",
                    isCurrent && "bg-primary text-primary-foreground scale-110 ring-4 ring-primary/20",
                    isUpcoming && "bg-muted text-muted-foreground"
                  )}
                >
                  {isCompleted ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    stepNumber
                  )}
                </div>

                {/* Step label */}
                <div className="mt-2 text-center">
                  <div
                    className={cn(
                      "text-xs font-medium transition-colors duration-300",
                      (isCompleted || isCurrent) && "text-foreground",
                      isUpcoming && "text-muted-foreground"
                    )}
                  >
                    {step.title}
                  </div>
                  {step.description && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {step.description}
                    </div>
                  )}
                </div>

                {/* Connecting line */}
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "absolute top-4 left-8 h-0.5 transition-all duration-500",
                      "w-[calc(100vw/3-4rem)]", // Responsive width
                      isCompleted && "bg-primary",
                      !isCompleted && "bg-muted"
                    )}
                    style={{
                      width: `calc((100% - 2rem) / ${steps.length - 1})`,
                      transform: 'translateX(0.5rem)'
                    }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Current step info */}
      <div className="text-center">
        <div className="text-sm text-muted-foreground">
          Étape {currentStep} sur {steps.length}
        </div>
        <div className="text-lg font-semibold text-foreground mt-1">
          {steps[currentStep - 1]?.title}
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
